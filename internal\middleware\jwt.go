package middleware

import (
	"chongli/component"
	"chongli/component/apollo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/jwt"
	"chongli/pkg/logger"
	resp "chongli/pkg/response"
	"github.com/gin-gonic/gin"
)

type JWTAuthMiddleware struct {
	log *logger.Logger
}

func NewJWTAuthMiddleware(
	bootStrap *component.BootStrap,
) *JWTAuthMiddleware {
	return &JWTAuthMiddleware{
		log: bootStrap.Log,
	}
}

// JWTAuth jwt authentication
func (jm *JWTAuthMiddleware) JWTAuth() gin.HandlerFunc {
	claimKey := "claim"
	return func(c *gin.Context) {
		token := c.Request.Header.Get("X-Token")
		if token == "" {
			err := errpkg.NewMiddleError(resp.PermissionDeniedError)
			resp.Response(c, nil, nil, err, resp.WithSLSLog)
			c.Abort()
			return
		}

		jwtSecret := apollo.GetApolloConfig().JwtSecret
		j := jwt.NewJWT(jwtSecret)
		claims, errParse := j.<PERSON>(token)
		if errParse != nil {
			// token过期
			if errParse.Error() == jwt.ValidationErrorExpired {
				err := errpkg.NewMiddleError(resp.TokenExpiredError)
				resp.Response(c, nil, nil, err, resp.WithSLSLog)
				c.Abort()
				return
			}
			err := errpkg.NewMiddleErrorWithCause(errParse, resp.TokenError)
			resp.Response(c, nil, nil, err, resp.WithSLSLog)
			c.Abort()
			return
		}

		// set claims to context.
		jm.setData2Context(c, claimKey, claims)
	}
}

// setData2Context set data to context.
func (jm *JWTAuthMiddleware) setData2Context(c *gin.Context, claimKey string, claims *jwt.CustomClaims) {
	// 将解析后的有效载荷claims重新写入gin.Context引用对象中
	// 将user_id 和 uuid 写进ctx
	meta := claims.MetaData
	if uid, ok := meta["user_id"]; !ok {
		err := errpkg.NewMiddleError(resp.TokenError)
		resp.Response(c, nil, nil, err, resp.WithSLSLog)
		c.Abort()
		return
	} else {
		c.Set("user_id", uid)
	}
	c.Set(claimKey, claims)
}
