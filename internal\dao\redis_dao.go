package dao

import (
	"chongli/component"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"context"
	"errors"
	"time"

	"github.com/go-redis/redis/v8"
)

type redisRepo struct {
	log *logger.Logger
	rds *redis.Client
}

// NewRedisRepo 创建 Redis 仓库实例
func NewRedisRepo(bootStrap *component.BootStrap) repo.RedisRepo {
	return &redisRepo{
		log: bootStrap.Log,
		rds: bootStrap.Driver.GetRdsClient(),
	}
}

// Get 从 Redis 获取值
func (r *redisRepo) Get(ctx context.Context, key string) (string, error) {
	result := r.rds.Get(ctx, key)
	if errors.Is(result.Err(), redis.Nil) {
		return "", nil // key 不存在，返回空字符串
	}
	if result.Err() != nil {
		r.log.Error("Redis Get 操作失败, key: %s, err: %v", key, result.Err())
		return "", result.Err()
	}
	return result.Val(), nil
}

// Set 设置值到 Redis
func (r *redisRepo) Set(ctx context.Context, key string, value string, expiration time.Duration) error {
	err := r.rds.Set(ctx, key, value, expiration).Err()
	if err != nil {
		r.log.Error("Redis Set 操作失败, key: %s, value: %s, expiration: %v, err: %v", key, value, expiration, err)
		return err
	}
	return nil
}

// Del 从 Redis 删除值
func (r *redisRepo) Del(ctx context.Context, key string) error {
	err := r.rds.Del(ctx, key).Err()
	if err != nil {
		r.log.Error("Redis Del 操作失败, key: %s, err: %v", key, err)
		return err
	}
	return nil
}

// Exists 检查 key 是否存在
func (r *redisRepo) Exists(ctx context.Context, key string) (bool, error) {
	result := r.rds.Exists(ctx, key)
	if result.Err() != nil {
		r.log.Error("Redis Exists 操作失败, key: %s, err: %v", key, result.Err())
		return false, result.Err()
	}
	return result.Val() > 0, nil
}

// SetNX 设置值到 Redis（仅当key不存在时）
func (r *redisRepo) SetNX(ctx context.Context, key string, value string, expiration time.Duration) error {
	result, err := r.rds.SetNX(ctx, key, value, expiration).Result()
	if err != nil {
		r.log.Error("Redis SetNX 操作失败, key: %s, value: %s, expiration: %v, err: %v", key, value, expiration, err)
		return err
	}
	// 检查返回值：true(1)表示设置成功，false(0)表示key已存在，设置失败
	if !result {
		return errors.New("key already exists")
	}
	return nil
}

// DelLock 安全删除分布式锁（使用Lua脚本确保原子性）
func (r *redisRepo) DelLock(ctx context.Context, key string, value string) error {
	// Lua脚本：只有当key存在且值等于value时才删除
	luaScript := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("del", KEYS[1])
		else
			return 0
		end
	`

	result := r.rds.Eval(ctx, luaScript, []string{key}, []string{value})
	if result.Err() != nil {
		r.log.Error("Redis DelLock 操作失败, key: %s, value: %s, err: %v", key, value, result.Err())
		return result.Err()
	}
	return nil
}
