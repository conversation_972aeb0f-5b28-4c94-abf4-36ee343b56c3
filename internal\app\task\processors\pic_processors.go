package processors

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/pkg/comfyui"
	"chongli/internal/pkg/tencentcloud"
	"chongli/internal/pkg/volcengine"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/pkg/getui"
	"chongli/pkg/logger"
	"chongli/pkg/qiniu"
	"chongli/pkg/utils"
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"strconv"
	"time"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	facefusion "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/facefusion/v20220927"
)

type PicTaskProcessors struct {
	bootstrap       *component.BootStrap
	taskService     *service.TaskService
	userWorkService *service.UserWorkService
	log             *logger.Logger
	userService     *service.UserService
}

// NewPicTaskProcessors 创建图片任务处理器实例
func NewPicTaskProcessors(
	bootstrap *component.BootStrap,
	taskService *service.TaskService,
	userWorkService *service.UserWorkService,
	userService *service.UserService,
) *PicTaskProcessors {
	return &PicTaskProcessors{
		bootstrap:       bootstrap,
		taskService:     taskService,
		userWorkService: userWorkService,
		log:             bootstrap.Log,
		userService:     userService,
	}
}

// 第一步

func (s *PicTaskProcessors) AiCompound() {
	ctx := context.Background()

	// 1. 创建ComfyUI客户端进行队列检查
	comfyClient := comfyui.NewClient(s.bootstrap.Config.AiDomain)

	// 2. 检查队列状态
	queueStatus, err := comfyClient.GetPromptQueue(ctx)
	if err != nil {
		s.log.Error("检查队列状态时出错: %v", err)
		// 即使检查队列失败，也继续执行，不中断流程
	} else {
		// 增加对queueStatus为nil的判断
		if queueStatus == nil {
			s.log.Info("队列状态返回为空，继续执行")
		} else if queueStatus.ExecInfo.QueueRemaining >= 2 {
			s.log.Error("当前有%d个任务在等待处理，队列繁忙，跳过此次执行", queueStatus.ExecInfo.QueueRemaining)
			return
		}
	}

	// 第一个事务：获取待处理的步骤
	var step *dto.TaskStepDTO

	step, err = s.taskService.GetPendingTaskStep(ctx, model.StepNameAiCompound, nil)
	if err != nil {
		s.log.Error("[AI合成] 获取待处理的步骤失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("[AI合成] 没有待处理的步骤")
		return
	}

	// 验证步骤参数
	params, err := s.validateAiCompoundParams(step)
	if err != nil {
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, errors.New("模版参数错误"))
		return
	}

	err = s.uploadImageToComfyUI(ctx, comfyClient, params.ReferenceImage)
	if err != nil {
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, errors.New("ai执行失败"))
		return
	}
	// 执行AI图片合成处理
	taskID, err := s.processAiCompound(ctx, comfyClient, params)
	if err != nil {
		step.ErrorMsg = fmt.Sprintf("AI图片合成处理失败: %v", err)
		s.handleStepError(ctx, step, errors.New("AI图片合成处理失败"))
		return
	}

	// 将task_id写入result并更新状态为waiting_result
	// 将map序列化为JSON字符串，再存入数据库
	resultJSON, err := json.Marshal(map[string]interface{}{
		"task_id": taskID,
	})
	if err != nil {
		s.log.Error("[AI合成] 任务ID:%d 步骤ID:%d 序列化结果数据失败: %v", step.WorkID, step.ID, err)
		step.ErrorMsg = fmt.Sprintf("序列化结果数据失败: %v", err)
		s.handleStepError(ctx, step, errors.New("序列化结果数据失败"))
		return
	}

	err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusWaitingResult, resultJSON)
	if err != nil {
		s.log.Error("[AI合成] 任务ID:%d 步骤ID:%d 更新步骤状态失败: %v", step.WorkID, step.ID, err)
		step.ErrorMsg = fmt.Sprintf("更新步骤状态失败: %v", err)
		s.handleStepError(ctx, step, errors.New("更新步骤状态失败"))
		return
	}

	s.log.Info("[AI合成] 任务ID:%d 步骤ID:%d AI图片合成任务已提交，task_id: %s", step.WorkID, step.ID, taskID)
}

// AddChangeStyle 处理AI图片合成结果
func (s *PicTaskProcessors) AddChangeStyle() {
	ctx := context.Background()

	// 1.获取状态是waiting_result，步骤名字是ai_compound的任务
	step, err := s.getWaitingResultStep(ctx, model.StepNameAiCompound)
	if err != nil {
		s.log.Error("[风格转换] 获取waiting_result状态的ai_compound任务失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("[风格转换] 没有waiting_result状态的ai_compound任务")
		return
	}

	// 2. 解析result字段中的task_id
	taskID, err := s.extractTaskIDFromResult(step.Result)
	if err != nil {
		step.ErrorMsg = fmt.Sprintf("解析task_id失败: %v", err)
		s.handleStepError(ctx, step, errors.New("解析ai结果失败"))
		return
	}
	comfyClient := comfyui.NewClient(s.bootstrap.Config.AiDomain)
	// 3.调用internal/pkg/comfyui/comfyui.go函数History 拿到图片地址
	historyResp, err := comfyClient.GetHistoryWithStatus(ctx, taskID)
	if err != nil {
		step.ErrorMsg = fmt.Sprintf("获取ComfyUI历史记录失败: %v", err)
		s.handleStepError(ctx, step, errors.New("获取ai结果失败"))
		return
	}

	// 检查任务状态
	if historyResp.Status == "running" {
		s.log.Info("[风格转换] 任务ID:%d 步骤ID:%d ComfyUI任务 %s 仍在运行中，跳过此次处理", step.WorkID, step.ID, taskID)
		return
	}

	if historyResp.Status == "failed" {
		step.ErrorMsg = fmt.Sprintf("ComfyUI任务失败: %s", historyResp.Message)
		s.handleStepError(ctx, step, errors.New("AI任务失败"))
		return
	}

	if historyResp.Status != "completed" || historyResp.ImageURL == "" {
		step.ErrorMsg = fmt.Sprintf("ComfyUI任务状态异常: %s", historyResp.Status)
		s.handleStepError(ctx, step, errors.New("AI任务失败"))
		return
	}

	// 4.使用七牛云FetchFile直接抓取图片
	filename, _ := s.generateTempFilename(historyResp.ImageURL)
	qiniuPath := fmt.Sprintf("chongli-ai-pic/%s/%s", time.Now().Format("2006/01/02"), filename)
	qiniuURL, err := qiniu.FetchFile(utils.EnsureHttpsPrefix(historyResp.ImageURL), qiniuPath)
	if err != nil {
		step.ErrorMsg = fmt.Sprintf("七牛云抓取图片失败: %v", err)
		s.handleStepError(ctx, step, errors.New("ai结果转存失败"))
		return
	}

	// 更新当前步骤为完成
	resultJSON, err := json.Marshal(map[string]any{
		"image_url": utils.EnsureHttpsPrefix(qiniuURL),
		"task_id":   taskID,
	})
	if err != nil {
		s.log.Error("[风格转换] 任务ID:%d 步骤ID:%d 序列化结果数据失败: %v", step.WorkID, step.ID, err)
		step.ErrorMsg = fmt.Sprintf("序列化结果数据失败: %v", err)
		s.handleStepError(ctx, step, errors.New("AI结果序列化失败"))
		return
	}

	// 获取下一步
	nextSteps, err := s.taskService.GetStepQuery(ctx, &dto.GetStepQuery{
		Step: &dto.TaskStepDTO{
			StepIndex: step.StepIndex + 1,
			WorkID:    step.WorkID,
		},
	})
	if err != nil {
		s.log.Error("[风格转换] 任务ID:%d 步骤ID:%d 获取下一步失败: %v", step.WorkID, step.ID, err)
		step.ErrorMsg = fmt.Sprintf("获取下一步失败: %v", err)
		s.handleStepError(ctx, step, errors.New("ai后续流程失败"))
		return
	}
	if nextSteps == nil {
		s.log.Info("[风格转换] 任务ID:%d 步骤ID:%d 任务已完成", step.WorkID, step.ID)
		return
	}
	if len(nextSteps) == 0 {
		s.log.Error("[风格转换] 任务ID:%d 步骤ID:%d 获取下一步失败: 获取到的下一步是0个", step.WorkID, step.ID)
		return
	}
	nextStep := nextSteps[0]
	// 检查下一步参数
	if nextStep.Params == nil {
		s.handleStepError(ctx, nextStep, errors.New("AI后续流程失败"))
		return
	}
	styleDesc, ok := nextStep.Params["style_desc"]
	if !ok {
		s.log.Error("AI后续流程失败：下一步参数中缺少style_desc")
		s.handleStepError(ctx, nextStep, errors.New("AI后续流程失败"))
		return
	}
	styleDescStr, ok := styleDesc.(string)
	if !ok {
		s.log.Error("AI后续流程失败：style_desc参数类型错误，应为string")
		s.handleStepError(ctx, nextStep, errors.New("AI后续流程失败"))
		return
	}
	if styleDescStr == "" {
		s.log.Error("AI后续流程失败：style_desc参数不能为空")
		s.handleStepError(ctx, nextStep, errors.New("AI后续流程失败"))
		return
	}

	// 提交火山引擎任务
	volcClient := volcengine.NewClient(&volcengine.Config{
		AccessKey: s.bootstrap.Config.VolcengineAccessKey,
		SecretKey: s.bootstrap.Config.VolcengineSecretKey,
	})
	volcReq := volcengine.NewSubmitTaskRequest(map[string]any{
		"image_urls": []string{utils.EnsureHttpsPrefix(qiniuURL)},
		"prompt":     styleDescStr,
		"req_key":    "seededit_v3.0",
	})
	volcResp, code, err := volcClient.CVSync2AsyncSubmitTask(volcReq)
	if err != nil {
		s.log.Error("AI后续流程失败：提交火山引擎任务失败: %v", err)
		s.handleStepError(ctx, nextStep, fmt.Errorf("提交AI后续流程失败"))
		return
	}
	if code == 429 {
		s.log.Warning("AI后续流程失败：火山忙: %v", volcResp.Message)
		s.handleStepError(ctx, nextStep, fmt.Errorf("提交AI后续流程失败"))
		return
	}
	if code != 200 {
		s.log.Error("AI后续流程失败：火山引擎任务提交失败: %v", volcResp.Message)
		s.handleStepError(ctx, nextStep, fmt.Errorf("提交AI后续流程失败"))
		return
	}

	err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusDone, resultJSON)
	if err != nil {
		s.log.Error("AI后续流程失败：更新步骤状态失败: %v", err)
		s.handleStepError(ctx, step, fmt.Errorf("更新步骤状态失败"))
		return
	}

	// 更新下一步骤状态
	nextResultJSON, err := json.Marshal(map[string]any{
		"task_id": volcResp.Data.TaskID,
	})
	if err != nil {
		s.log.Error("AI后续流程失败：序列化下一步结果数据失败: %v", err)
		s.handleStepError(ctx, nextStep, fmt.Errorf("序列化下一步结果数据失败"))
		return
	}
	err = s.taskService.UpdateStepStatusAndResult(ctx, nextStep, model.StepStatusWaitingResult, nextResultJSON)
	if err != nil {
		s.log.Error("AI后续流程失败：更新下一步骤状态失败: %v", err)
		s.handleStepError(ctx, nextStep, fmt.Errorf("更新下一步骤状态失败"))
		return
	}

	s.log.Info("AI后续流程成功：AI图片风格转换任务已提交，task_id: %s", volcResp.Data.TaskID)
}
func (s *PicTaskProcessors) GetStyleResultAndChangeFace() {
	ctx := context.Background()

	// 1.获取状态是waiting_result，步骤名字是change_style的任务
	step, err := s.getWaitingResultStep(ctx, model.StepNameChangeStyle)
	if err != nil {
		s.log.Error("[换脸] 获取waiting_result状态的change_style任务失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("[换脸] 没有waiting_result状态的change_style任务")
		return
	}

	// 2.解析result字段中的task_id
	taskID, err := s.extractTaskIDFromResult(step.Result)
	if err != nil {
		step.ErrorMsg = err.Error()
		s.log.Error("[换脸] 任务ID解析失败: %v", err)
		s.handleStepError(ctx, step, errors.New("任务ID解析失败"))
		return
	}
	// 提交火山引擎任务
	volcClient := volcengine.NewClient(&volcengine.Config{
		AccessKey: s.bootstrap.Config.VolcengineAccessKey,
		SecretKey: s.bootstrap.Config.VolcengineSecretKey,
	})
	volcReq := &volcengine.CVSync2AsyncGetResultRequest{
		ReqKey: "seededit_v3.0",
		TaskID: taskID,
	}
	volcResp, err := volcClient.CVSync2AsyncGetResult(volcReq)
	if err != nil {
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, errors.New("任务提交失败"))
		return
	}
	if volcResp != nil && len(volcResp.Data.ImageUrls) > 0 {

		resultJSON, err := json.Marshal(map[string]any{
			"image_url": volcResp.Data.ImageUrls[0],
			"task_id":   taskID,
		})
		if err != nil {
			s.log.Error("[换脸] 任务ID:%d 步骤ID:%d 序列化结果数据失败: %v", step.WorkID, step.ID, err)
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, fmt.Errorf("序列化结果数据失败"))
			return
		}

		// 获取下一步
		nextSteps, err := s.taskService.GetStepQuery(ctx, &dto.GetStepQuery{
			Step: &dto.TaskStepDTO{
				StepIndex: step.StepIndex + 1,
				WorkID:    step.WorkID,
			},
		})
		if err != nil {
			s.log.Error("[换脸] 任务ID:%d 步骤ID:%d 获取下一步失败: %v", step.WorkID, step.ID, err)
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, fmt.Errorf("获取下一步失败"))
			return
		}
		if nextSteps == nil {
			s.log.Info("[换脸] 任务ID:%d 步骤ID:%d 任务已完成", step.WorkID, step.ID)
			return
		}
		if len(nextSteps) == 0 {
			s.log.Error("[换脸] 任务ID:%d 步骤ID:%d 获取下一步失败: 获取到的下一步是0个", step.WorkID, step.ID)
			return
		}
		nextStep := nextSteps[0]
		// 检查下一步参数
		if nextStep.Params == nil {
			nextStep.ErrorMsg = "下一步参数为空"
			s.handleStepError(ctx, nextStep, errors.New("参数缺失"))
			return
		}
		persionPic, ok := nextStep.Params["person_pic"]
		if !ok {
			nextStep.ErrorMsg = "下一步参数中缺少person_pic"
			s.handleStepError(ctx, nextStep, errors.New("参数缺失"))
			return
		}
		persionPicStr, ok := persionPic.(string)
		if !ok {
			nextStep.ErrorMsg = "person_pic参数类型错误，应为string"
			s.handleStepError(ctx, nextStep, errors.New("参数类型错误"))
			return
		}
		s.log.Info("[换脸] 任务ID:%d 步骤ID:%d 开始人脸融合，用户图片: %s", nextStep.WorkID, nextStep.ID, persionPicStr)

		// 调用腾讯云人脸融合API进行换脸
		fusedImageURL, err := s.performFaceFusion(ctx, volcResp.Data.ImageUrls[0], persionPicStr, nextStep)
		if err != nil {
			nextStep.ErrorMsg = err.Error()
			s.handleStepError(ctx, nextStep, errors.New("人像清晰度不足，请重新上传"))
			return
		}

		// 使用七牛云FetchFile直接抓取融合后的图片
		filename, _ := s.generateTempFilename(fusedImageURL)
		qiniuPath := fmt.Sprintf("chongli-ai-pic/%s/%s", time.Now().Format("2006/01/02"), filename)
		finalQiniuURL, err := qiniu.FetchFile(fusedImageURL, qiniuPath)
		if err != nil {
			nextStep.ErrorMsg = err.Error()
			s.handleStepError(ctx, nextStep, errors.New("图片上传失败"))
			return
		}
		err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusDone, resultJSON)
		if err != nil {
			s.log.Error("[换脸] 任务ID:%d 步骤ID:%d 更新步骤状态失败: %v", step.WorkID, step.ID, err)
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, errors.New("状态更新失败"))
			return
		}
		// 更新下一步骤为完成状态
		nextResultJSON, err := json.Marshal(map[string]any{
			"image_url": utils.EnsureHttpsPrefix(finalQiniuURL),
		})
		if err != nil {
			s.log.Error("[换脸] 任务ID:%d 步骤ID:%d 序列化下一步结果数据失败: %v", nextStep.WorkID, nextStep.ID, err)
			nextStep.ErrorMsg = err.Error()
			s.handleStepError(ctx, nextStep, errors.New("数据序列化失败"))
			return
		}

		err = s.taskService.UpdateStepStatusAndResult(ctx, nextStep, model.StepStatusDone, nextResultJSON)
		if err != nil {
			s.log.Error("[换脸] 任务ID:%d 步骤ID:%d 更新下一步骤状态失败: %v", nextStep.WorkID, nextStep.ID, err)
			nextStep.ErrorMsg = err.Error()
			s.handleStepError(ctx, nextStep, errors.New("状态更新失败"))
			return
		}

		// 完成整个任务，创建用户作品
		err = s.completeTaskAndCreateUserWork(ctx, nextStep, utils.EnsureHttpsPrefix(finalQiniuURL))
		if err != nil {
			s.log.Error("[换脸] 任务ID:%d 步骤ID:%d 完成任务并创建用户作品失败: %v", nextStep.WorkID, nextStep.ID, err)
		}
		nextStep.UserWork.PicURL = utils.EnsureHttpsPrefix(finalQiniuURL)
		_ = s.pushGetui(nextStep, true)
		s.log.Info("[换脸] 任务ID:%d 步骤ID:%d 换脸任务已完成，最终图片URL: %s", nextStep.WorkID, nextStep.ID, finalQiniuURL)
	}
}

func (s *PicTaskProcessors) pushGetui(step *dto.TaskStepDTO, isSuccess bool) error {
	state := 1
	msgString := "写真制作成功"
	if !isSuccess {
		state = 2
		msgString = "写真制作失败"
	}
	payload := getui.MakePhotoPayload{
		WorkID:     step.WorkID,
		State:      state,
		PhotoCover: step.UserWork.PicURL,
	}
	msg := getui.Transmission{
		Type: getui.MakePhoto,
		Msg:  msgString,
		Time: time.Now().UnixMilli(),
	}
	msg.Payload = payload
	// 发送推送
	err := s.userService.PushGetuiByUserId(step.UserWork.UserID, msg)
	if err != nil {
		return err
	}
	return nil
}

// downloadImage 下载图片到本地文件
func (s *PicTaskProcessors) downloadImage(url, filepath string) error {
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("请求图片失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载图片失败，状态码: %d", resp.StatusCode)
	}

	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer file.Close()

	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("保存图片失败: %w", err)
	}

	return nil
}

// generateTempFilename 从 URL 生成临时文件名和路径
func (s *PicTaskProcessors) generateTempFilename(urlStr string) (string, string) {
	// 解析URL
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		// 如果URL解析失败，使用默认值
		hasher := md5.New()
		hasher.Write([]byte(urlStr))
		hashBytes := hasher.Sum(nil)
		hashString := fmt.Sprintf("%x", hashBytes)
		filename := hashString + ".jpg"
		tempDir := os.TempDir()
		tempFilePath := filepath.Join(tempDir, filename)
		return filename, tempFilePath
	}

	// 使用path包获取文件名和后缀
	filename := path.Base(parsedURL.Path)
	ext := path.Ext(filename)

	// 如果没有后缀或后缀为空，使用默认后缀
	if ext == "" {
		ext = ".jpg"
	}

	// 使用 URL 计算 MD5 作为文件名
	hasher := md5.New()
	hasher.Write([]byte(urlStr))
	hashBytes := hasher.Sum(nil)
	hashString := fmt.Sprintf("%x", hashBytes)

	// 创建临时文件路径
	tempDir := os.TempDir()
	tempFilePath := filepath.Join(tempDir, hashString+ext)

	return hashString + ext, tempFilePath
}

// uploadImageToComfyUI 上传图片到ComfyUI
func (s *PicTaskProcessors) uploadImageToComfyUI(ctx context.Context, client *comfyui.Client, fileurl string) error {
	// 生成文件名和临时文件路径
	filename, tempFilePath := s.generateTempFilename(fileurl)

	// 下载图片到本地
	err := s.downloadImage(fileurl, tempFilePath)
	if err != nil {
		return fmt.Errorf("下载图片失败: %w", err)
	}
	defer os.Remove(tempFilePath) // 函数执行完后删除临时文件

	// 打开文件准备上传
	file, err := os.Open(tempFilePath)
	if err != nil {
		return fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	_, err = client.UploadImage(ctx, file, filename)
	if err != nil {
		return fmt.Errorf("上传图片失败: %w", err)
	}

	return nil
}

// AiCompoundParams AI合成参数结构体
type AiCompoundParams struct {
	PetDescription         string `json:"petDescription"`
	BackgroundDescription  string `json:"backgroundDescription"`
	CompositionDescription string `json:"compositionDescription"`
	LoraSelection          string `json:"loraSelection"`
	ModelStrength          string `json:"modelStrength"`
	ReferenceImage         string `json:"referenceImage"`
	WorkflowUrl            string `json:"workflow_url"`
}

// validateAiCompoundParams 验证AI合成步骤参数
func (s *PicTaskProcessors) validateAiCompoundParams(step *dto.TaskStepDTO) (*AiCompoundParams, error) {
	// 获取pet_pic图片地址
	petPic, ok := step.Params["pet_pic"].(string)
	if !ok || petPic == "" {
		return nil, fmt.Errorf("pet_pic参数缺失或格式错误")
	}

	// 获取pet_desc
	petDesc, ok := step.Params["pet_desc"].(string)
	if !ok || petDesc == "" {
		return nil, fmt.Errorf("pet_desc参数缺失或格式错误")
	}

	// 获取background_desc
	backgroundDesc, ok := step.Params["background_desc"].(string)
	if !ok || backgroundDesc == "" {
		return nil, fmt.Errorf("background_desc参数缺失或格式错误")
	}

	// 获取composition_desc
	compositionDesc, ok := step.Params["composition_desc"].(string)
	if !ok || compositionDesc == "" {
		return nil, fmt.Errorf("composition_desc参数缺失或格式错误")
	}

	// 获取style (对应lora_selection)
	style, ok := step.Params["style"].(string)
	if !ok || style == "" {
		return nil, fmt.Errorf("style参数缺失或格式错误")
	}
	workflowUrl, ok := step.Params["workflow_url"].(string)
	if !ok || workflowUrl == "" {
		return nil, fmt.Errorf("workflow_url参数缺失或格式错误")
	}
	// 获取strength (对应model_strength)
	var strengthStr string
	if strength, ok := step.Params["strength"].(float64); ok {
		strengthStr = strconv.FormatFloat(strength, 'f', -1, 64)
	} else if strength, ok := step.Params["strength"].(string); ok {
		strengthStr = strength
	} else {
		strengthStr = "1" // 默认值
	}

	return &AiCompoundParams{
		PetDescription:         petDesc,
		BackgroundDescription:  backgroundDesc,
		CompositionDescription: compositionDesc,
		LoraSelection:          style,
		ModelStrength:          strengthStr,
		ReferenceImage:         petPic,
		WorkflowUrl:            workflowUrl,
	}, nil
}

// processAiCompound 实现AI图片合成的核心逻辑（基于Python版本）
func (s *PicTaskProcessors) processAiCompound(ctx context.Context, comfyClient *comfyui.Client, params *AiCompoundParams) (string, error) {

	workflowPath, err := s.downloadWorkflowWithOutCache(params.WorkflowUrl)
	if err != nil {
		return "", fmt.Errorf("下载workflow文件失败: %w", err)
	}

	// 生成唯一客户端ID
	clientID := fmt.Sprintf("go-client-%d", time.Now().UnixNano())

	// 读取pet.json模板文件
	promptData, err := s.loadPetJsonTemplate(workflowPath)
	if err != nil {
		return "", fmt.Errorf("读取pet.json模板失败: %w", err)
	}

	// 替换模板中的参数
	err = s.replacePromptParams(promptData, params)
	if err != nil {
		return "", fmt.Errorf("替换模板参数失败: %w", err)
	}

	// 设置随机seed值
	s.setRandomSeeds(promptData)

	// 发送prompt请求到ComfyUI
	promptResp, err := comfyClient.Prompt(ctx, clientID, promptData)
	if err != nil {
		return "", fmt.Errorf("发送prompt请求失败: %w", err)
	}

	// 返回prompt_id作为task_id
	return promptResp.PromptID, nil
}

// loadPetJsonTemplate 读取pet.json模板文件
func (s *PicTaskProcessors) loadPetJsonTemplate(workflowPath string) (map[string]interface{}, error) {
	// 读取pet.json文件
	data, err := os.ReadFile(workflowPath)
	if err != nil {
		return nil, fmt.Errorf("读取pet.json文件失败: %w", err)
	}

	var promptData map[string]interface{}
	if err := json.Unmarshal(data, &promptData); err != nil {
		return nil, fmt.Errorf("解析pet.json文件失败: %w", err)
	}

	return promptData, nil
}

// replacePromptParams 替换模板中的参数，基于_meta.title动态查找节点
func (s *PicTaskProcessors) replacePromptParams(promptData map[string]interface{}, params *AiCompoundParams) error {
	// 跟踪各个节点是否找到
	petActionNodeFound := false
	backgroundNodeFound := false
	compositionNodeFound := false
	petImageNodeFound := false
	loraNodeFound := false
	loraCharacterNodeFound := false

	filename, _ := s.generateTempFilename(params.ReferenceImage)
	strength, err := strconv.ParseFloat(params.ModelStrength, 64)
	if err != nil {
		strength = 1.0 // 默认值
	}

	// 遍历所有节点，基于_meta.title查找对应节点
	for _, nodeData := range promptData {
		nodeMap, ok := nodeData.(map[string]interface{})
		if !ok {
			continue
		}

		// 检查是否有_meta和inputs字段
		metaData, hasMeta := nodeMap["_meta"]
		inputs, hasInputs := nodeMap["inputs"]
		if !hasMeta || !hasInputs {
			continue
		}

		metaMap, ok := metaData.(map[string]interface{})
		if !ok {
			continue
		}

		titleInterface, hasTitle := metaMap["title"]
		if !hasTitle {
			continue
		}

		title, ok := titleInterface.(string)
		if !ok {
			continue
		}

		inputsMap, ok := inputs.(map[string]interface{})
		if !ok {
			continue
		}

		// 根据title匹配不同的节点类型并设置对应参数
		switch title {
		case "宠物动作提示词":
			if _, hasTextB := inputsMap["text_b"]; hasTextB {
				inputsMap["text_b"] = params.PetDescription
				petActionNodeFound = true
			}
		case "背景风格提示词":
			if _, hasTextB := inputsMap["text_b"]; hasTextB {
				inputsMap["text_b"] = params.BackgroundDescription
				backgroundNodeFound = true
			}
		case "人物提示词":
			if _, hasTextB := inputsMap["text_b"]; hasTextB {
				inputsMap["text_b"] = params.CompositionDescription
				compositionNodeFound = true
			}
		case "加载宠物图像":
			if _, hasImage := inputsMap["image"]; hasImage {
				inputsMap["image"] = filename
				petImageNodeFound = true
			}
		case "LoRA加载器-背景风格":
			if _, hasLoraName := inputsMap["lora_name"]; hasLoraName {
				inputsMap["lora_name"] = params.LoraSelection
				loraNodeFound = true
			}
		case "LoRA加载器-人物":
			if _, hasStrengthModel := inputsMap["strength_model"]; hasStrengthModel {
				inputsMap["strength_model"] = strength
				loraCharacterNodeFound = true
			}
		}
	}

	// 检查是否所有必需的节点都找到了
	if !petActionNodeFound {
		return fmt.Errorf("pet.json中没有找到宠物动作提示词节点")
	}
	if !backgroundNodeFound {
		return fmt.Errorf("pet.json中没有找到背景风格提示词节点")
	}
	if !compositionNodeFound {
		return fmt.Errorf("pet.json中没有找到人物提示词节点")
	}
	if !petImageNodeFound {
		return fmt.Errorf("pet.json中没有找到加载宠物图像节点")
	}
	if !loraNodeFound {
		return fmt.Errorf("pet.json中没有找到LoRA加载器-背景风格节点")
	}
	if !loraCharacterNodeFound {
		return fmt.Errorf("pet.json中没有找到LoRA加载器-人物节点")
	}

	return nil
}

// setRandomSeeds 设置随机seed值，基于_meta.title查找K采样器节点
func (s *PicTaskProcessors) setRandomSeeds(promptData map[string]interface{}) {
	// 遍历所有节点，查找标题为"K采样器"的节点
	for nodeID, nodeData := range promptData {
		nodeMap, ok := nodeData.(map[string]interface{})
		if !ok {
			continue
		}

		// 检查是否有_meta和inputs字段
		metaData, hasMeta := nodeMap["_meta"]
		inputs, hasInputs := nodeMap["inputs"]
		if !hasMeta || !hasInputs {
			continue
		}

		metaMap, ok := metaData.(map[string]interface{})
		if !ok {
			continue
		}

		titleInterface, hasTitle := metaMap["title"]
		if !hasTitle {
			continue
		}

		title, ok := titleInterface.(string)
		if !ok {
			continue
		}

		inputsMap, ok := inputs.(map[string]interface{})
		if !ok {
			continue
		}

		// 如果是K采样器节点且有seed参数，设置随机seed值
		if title == "K采样器" {
			if _, hasSeed := inputsMap["seed"]; hasSeed {
				seed := s.generateRandomSeed()
				inputsMap["seed"] = seed
				s.log.Info("[参数替换] K采样器节点 %s 设置随机seed: %d", nodeID, seed)
			}
		}
	}
}

// generateRandomSeed 生成15位随机数字
func (s *PicTaskProcessors) generateRandomSeed() int64 {
	// 生成100000000000000到999999999999999之间的随机数
	minNum := int64(100000000000000)
	maxNum := int64(999999999999999)
	return minNum + (time.Now().UnixNano() % (maxNum - minNum + 1))
}

// handleStepError 统一处理步骤失败的错误
func (s *PicTaskProcessors) handleStepError(ctx context.Context, step *dto.TaskStepDTO, taskError error) {
	// 获取调用者的文件名和行号
	_, file, line, ok := runtime.Caller(1)
	if ok {
		// 只保留文件名，不包含完整路径
		filename := filepath.Base(file)
		// 在ErrorMsg中添加调用位置信息
		if step.ErrorMsg != "" {
			step.ErrorMsg = fmt.Sprintf("%s [%s:%d]", step.ErrorMsg, filename, line)
		} else {
			step.ErrorMsg = fmt.Sprintf("错误位置: %s:%d", filename, line)
		}
	}

	s.log.Error("[步骤失败] 任务ID:%d 步骤ID:%d 处理步骤失败: %v", step.WorkID, step.ID, taskError)

	err := s.taskService.StepFail(ctx, step, taskError)
	if err != nil {
		s.log.Error("[步骤失败] 任务ID:%d 步骤ID:%d 标记步骤失败时出错: %v", step.WorkID, step.ID, err)
	}
	//_ = s.pushGetui(step, false)
}

// downloadWorkflowWithOutCache 下载workflow文件（不使用缓存），并为URL追加随机参数以避免任何缓存
func (s *PicTaskProcessors) downloadWorkflowWithOutCache(rawURL string) (string, error) {
	// 解析URL并追加防缓存查询参数
	parsed, err := url.Parse(rawURL)
	if err != nil {
		return "", fmt.Errorf("解析URL失败: %w", err)
	}
	q := parsed.Query()
	q.Set("_cb", fmt.Sprintf("%d", time.Now().UnixNano()))
	parsed.RawQuery = q.Encode()

	// 创建唯一临时文件
	tempFile, err := os.CreateTemp("", "workflow_*.json")
	if err != nil {
		return "", fmt.Errorf("创建临时文件失败: %w", err)
	}
	tempPath := tempFile.Name()
	_ = tempFile.Close()

	// 下载workflow文件到临时文件
	s.log.Info("[工作流下载] 强制直连，追加随机参数后下载: %s", parsed.String())
	if err := s.downloadImage(parsed.String(), tempPath); err != nil {
		_ = os.Remove(tempPath)
		return "", fmt.Errorf("下载workflow文件失败: %w", err)
	}

	return tempPath, nil
}

// getWaitingResultStep 获取状态为waiting_result且步骤名为指定名称的任务步骤
func (s *PicTaskProcessors) getWaitingResultStep(ctx context.Context, stepName string) (*dto.TaskStepDTO, error) {
	limit := 1
	query := &dto.GetStepQuery{
		Step: &dto.TaskStepDTO{
			Status:   model.StepStatusWaitingResult,
			StepName: stepName,
			UserWork: &dto.UserWorks{
				Status: 0,
			},
		},
		Limit: &limit,
	}

	stepDTOs, err := s.taskService.GetStepQuery(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("获取waiting_result状态的步骤失败: %w", err)
	}

	if len(stepDTOs) == 0 {
		return nil, nil // 没有找到对应的步骤
	}

	return stepDTOs[0], nil
}

// extractTaskIDFromResult 从result字段中解析task_id
func (s *PicTaskProcessors) extractTaskIDFromResult(result dto.JSONMap) (string, error) {
	if result == nil {
		return "", fmt.Errorf("result字段为空")
	}

	taskID, ok := result["task_id"].(string)
	if !ok {
		return "", fmt.Errorf("result中没有找到task_id或类型错误")
	}

	if taskID == "" {
		return "", fmt.Errorf("task_id为空")
	}

	return taskID, nil
}

// performFaceFusion 使用腾讯云人脸融合API进行换脸操作
func (s *PicTaskProcessors) performFaceFusion(ctx context.Context, modelImageURL, userImageURL string, step *dto.TaskStepDTO) (string, error) {
	// 创建腾讯云人脸融合客户端
	client, err := tencentcloud.NewFaceFusionClient(
		s.bootstrap.Config.TencentSecretId,
		s.bootstrap.Config.TencentSecretKey,
		"ap-chengdu",
	)
	if err != nil {
		return "", fmt.Errorf("创建腾讯云客户端失败: %w", err)
	}

	// 获取用户信息
	userId := step.UserWork.UserID
	user, err := s.userService.GetUserInfoByUid(int64(userId))
	if err != nil {
		s.log.Error("[人脸融合] 获取用户信息失败: %v", err)
		return "", fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 创建请求
	request := tencentcloud.NewFuseFaceProRequest()
	request.RspImgType = common.StringPtr("url")
	request.ModelUrl = common.StringPtr(modelImageURL)
	if user.IsVip == model.IsVip {
		request.LogoAdd = common.Int64Ptr(0)
	} else {
		request.LogoAdd = common.Int64Ptr(1)
		request.LogoParam = &facefusion.LogoParam{
			LogoUrl: common.StringPtr("https://chongli-cdn.51wnl-cq.com/workflow/water.png"),
			LogoRect: &facefusion.FaceRect{
				X: common.Int64Ptr(720),
				Y: common.Int64Ptr(1100),
			},
		}
	}
	request.MergeInfos = []*facefusion.MergeInfo{
		{
			Url: common.StringPtr(userImageURL),
		},
	}

	// 调用人脸融合API
	response, err := client.FuseFacePro(ctx, request)
	if err != nil {
		return "", err
	}

	if response.Response.FusedImage == nil {
		return "", fmt.Errorf("人脸融合返回结果为空")
	}

	return *response.Response.FusedImage, nil
}

// completeTaskAndCreateUserWork 完成任务并创建用户作品
func (s *PicTaskProcessors) completeTaskAndCreateUserWork(ctx context.Context, step *dto.TaskStepDTO, finalImageURL string) error {

	// 如果存在UserWorkID，则更新用户作品状态
	if step.WorkID > 0 {
		updates := map[string]any{
			"pic_url": finalImageURL,
			"cover":   finalImageURL, // 使用最终图片URL作为封面
		}

		// 使用已封装的用户作品服务完成用户作品
		err := s.userWorkService.DoneUserWork(ctx, step.WorkID, updates)
		if err != nil {
			return fmt.Errorf("更新用户作品失败: %w", err)
		}

		s.log.Info("[用户作品] 任务ID:%d 步骤ID:%d 用户作品已完成，WorkID: %d, 图片URL: %s", step.WorkID, step.ID, step.WorkID, finalImageURL)
	}

	return nil
}
