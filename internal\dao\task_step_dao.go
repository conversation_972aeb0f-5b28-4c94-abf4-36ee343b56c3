package dao

import (
	"context"

	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"

	"gorm.io/gorm"
)

type taskStepDAO struct {
	db *gorm.DB
}

func NewTaskStepRepo(component *component.BootStrap) repo.TaskStepRepo {
	return &taskStepDAO{db: component.Driver.GetMysqlDb()}
}

// getDB 根据可选的事务参数返回对应的数据库连接
func (d *taskStepDAO) getDB(tx ...*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

func (d *taskStepDAO) GetTaskStepByWorkId(ctx context.Context, workId int64, tx ...*gorm.DB) ([]*dto.TaskStepDTO, error) {
	var steps []*model.TaskStep
	err := d.getDB(tx...).WithContext(ctx).Where("work_id = ?", workId).Find(&steps).Error
	if err != nil {
		return nil, err
	}

	// 转换为 DTO
	stepDTOs := make([]*dto.TaskStepDTO, len(steps))
	for i, step := range steps {
		stepDTOs[i] = d.modelToDTO(step)
	}

	return stepDTOs, nil
}

func (d *taskStepDAO) Create(ctx context.Context, step *dto.TaskStepDTO, tx ...*gorm.DB) error {
	modelStep := d.dtoToModel(step)
	if err := d.getDB(tx...).WithContext(ctx).Create(modelStep).Error; err != nil {
		return err
	}
	// 将生成的 ID 回写到 DTO
	step.ID = modelStep.ID
	return nil
}

func (d *taskStepDAO) CreateBatch(ctx context.Context, steps []*dto.TaskStepDTO, tx ...*gorm.DB) error {
	modelSteps := make([]*model.TaskStep, len(steps))
	for i, step := range steps {
		modelSteps[i] = d.dtoToModel(step)
	}

	if err := d.getDB(tx...).WithContext(ctx).Create(modelSteps).Error; err != nil {
		return err
	}

	// 将生成的 ID 回写到 DTO
	for i, modelStep := range modelSteps {
		steps[i].ID = modelStep.ID
	}
	return nil
}

func (d *taskStepDAO) Update(ctx context.Context, id int64, updates map[string]interface{}, tx ...*gorm.DB) error {
	return d.getDB(tx...).WithContext(ctx).Model(&model.TaskStep{}).Where("id = ?", id).Updates(updates).Error
}

func (d *taskStepDAO) Delete(ctx context.Context, id int64, tx ...*gorm.DB) error {
	return d.getDB(tx...).WithContext(ctx).Where("id = ?", id).Delete(&model.TaskStep{}).Error
}

func (d *taskStepDAO) DeleteByTaskID(ctx context.Context, taskID int64, tx ...*gorm.DB) error {
	return d.getDB(tx...).WithContext(ctx).Where("task_id = ?", taskID).Delete(&model.TaskStep{}).Error
}

func (d *taskStepDAO) GetStepQuery(ctx context.Context, query *dto.GetStepQuery, tx ...*gorm.DB) ([]*dto.TaskStepDTO, error) {
	var steps []*model.TaskStep

	db := d.getDB(tx...).WithContext(ctx).Model(&model.TaskStep{}).Preload("UserWork")

	// 根据 Step 条件构建查询
	if query != nil {
		step := query.Step

		if step.ID != 0 {
			db = db.Where("task_steps.id = ?", step.ID)
		}

		if step.StepName != "" {
			db = db.Where("task_steps.step_name = ?", step.StepName)
		}

		if step.Status != "" {
			db = db.Where("task_steps.status = ?", step.Status)
		}

		if step.StepIndex != 0 {
			db = db.Where("task_steps.step_index = ?", step.StepIndex)
		}
		if step.WorkID != 0 {
			db = db.Where("task_steps.work_id = ?", step.WorkID)
		}

		// 根据 UserWorks 条件构建连表查询
		if query.Step.UserWork != nil {
			userWork := query.Step.UserWork

			if userWork.ID != 0 {
				db = db.Joins("JOIN user_works ON task_steps.work_id = user_works.id").Where("user_works.id = ?", userWork.ID)
			}

			if userWork.UserID != 0 {
				db = db.Joins("JOIN user_works ON task_steps.work_id = user_works.id").Where("user_works.user_id = ?", userWork.UserID)
			}

			if userWork.WorkType != "" {
				db = db.Joins("JOIN user_works ON task_steps.work_id = user_works.id").Where("user_works.work_type = ?", userWork.WorkType)
			}

			if userWork.Status == 0 || userWork.Status == 1 || userWork.Status == -1 {
				db = db.Joins("JOIN user_works ON task_steps.work_id = user_works.id").Where("user_works.status = ?", userWork.Status)
			}

			if userWork.TemplateID != 0 {
				db = db.Joins("JOIN user_works ON task_steps.work_id = user_works.id").Where("user_works.template_id = ?", userWork.TemplateID)
			}
		}
	}

	// 默认按创建时间排序
	db = db.Order("task_steps.created_at ASC")

	// 限制返回数量
	if query.Limit != nil && *query.Limit > 0 {
		db = db.Limit(*query.Limit)
	}

	err := db.Find(&steps).Error
	if err != nil {
		return nil, err
	}

	// 转换为 DTO
	stepDTOs := make([]*dto.TaskStepDTO, len(steps))
	for i, step := range steps {
		stepDTOs[i] = d.modelToDTO(step)
	}

	return stepDTOs, nil
}

func (d *taskStepDAO) Count(ctx context.Context, query *dto.GetStepQuery, tx ...*gorm.DB) (int64, error) {
	var count int64
	db := d.getDB(tx...).WithContext(ctx).Model(&model.TaskStep{})

	// 根据 Step 条件构建查询
	if query != nil {
		step := query.Step

		if step.ID != 0 {
			db = db.Where("task_steps.id = ?", step.ID)
		}

		if step.StepName != "" {
			db = db.Where("task_steps.step_name = ?", step.StepName)
		}

		if step.Status != "" {
			db = db.Where("task_steps.status = ?", step.Status)
		}

		if step.StepIndex != 0 {
			db = db.Where("task_steps.step_index = ?", step.StepIndex)
		}
		if step.WorkID != 0 {
			db = db.Where("task_steps.work_id = ?", step.WorkID)
		}

		// 根据 UserWorks 条件构建连表查询
		if query.Step.UserWork != nil {
			userWork := query.Step.UserWork

			if userWork.ID != 0 {
				db = db.Joins("JOIN user_works ON task_steps.work_id = user_works.id").Where("user_works.id = ?", userWork.ID)
			}

			if userWork.UserID != 0 {
				db = db.Joins("JOIN user_works ON task_steps.work_id = user_works.id").Where("user_works.user_id = ?", userWork.UserID)
			}

			if userWork.WorkType != "" {
				db = db.Joins("JOIN user_works ON task_steps.work_id = user_works.id").Where("user_works.work_type = ?", userWork.WorkType)
			}

			if userWork.Status == 0 || userWork.Status == 1 || userWork.Status == -1 {
				db = db.Joins("JOIN user_works ON task_steps.work_id = user_works.id").Where("user_works.status = ?", userWork.Status)
			}

			if userWork.TemplateID != 0 {
				db = db.Joins("JOIN user_works ON task_steps.work_id = user_works.id").Where("user_works.template_id = ?", userWork.TemplateID)
			}
		}
	}

	err := db.Count(&count).Error
	return count, err
}

// modelToDTO 将 model.TaskStep 转换为 dto.TaskStepDTO
func (d *taskStepDAO) modelToDTO(step *model.TaskStep) *dto.TaskStepDTO {
	if step == nil {
		return nil
	}
	stepDTO := &dto.TaskStepDTO{
		ID:         step.ID,
		StepIndex:  step.StepIndex,
		StepName:   step.StepName,
		Status:     step.Status,
		RetryCount: step.RetryCount,
		Params:     dto.JSONMap(step.Params),
		Result:     dto.JSONMap(step.Result),
		ErrorMsg:   step.ErrorMsg,
		StartedAt:  step.StartedAt,
		FinishedAt: step.FinishedAt,
		CreatedAt:  step.CreatedAt,
		UpdatedAt:  step.UpdatedAt,
		WorkID:     step.WorkID,
	}
	// 如果 model 的 UserWork 不为空（已 preload），则转换并装入到 TaskStepDTO 的 UserWork 中
	if step.UserWork.ID != 0 {
		stepDTO.UserWork = ModelUserWorksToDTO(&step.UserWork)
	}

	return stepDTO
}

// dtoToModel 将 dto.TaskStepDTO 转换为 model.TaskStep
func (d *taskStepDAO) dtoToModel(step *dto.TaskStepDTO) *model.TaskStep {
	if step == nil {
		return nil
	}
	return &model.TaskStep{
		ID:         step.ID,
		WorkID:     step.WorkID,
		StepIndex:  step.StepIndex,
		StepName:   step.StepName,
		Status:     step.Status,
		RetryCount: step.RetryCount,
		Params:     model.JSONMap(step.Params),
		Result:     model.JSONMap(step.Result),
		ErrorMsg:   step.ErrorMsg,
		StartedAt:  step.StartedAt,
		FinishedAt: step.FinishedAt,
		CreatedAt:  step.CreatedAt,
		UpdatedAt:  step.UpdatedAt,
	}
}
