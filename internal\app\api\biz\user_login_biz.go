package biz

import (
	"chongli/component"
	"chongli/component/apollo"
	"chongli/component/driver"
	service2 "chongli/internal/service"
	"chongli/internal/service/constant"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/getui"
	"chongli/pkg/ip"
	"chongli/pkg/jwt"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/sms"
	"chongli/pkg/utils"
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"github.com/panjf2000/ants/v2"
)

type UserLoginService struct {
	log           *logger.Logger
	tx            driver.ITransaction
	configService *service2.ConfigService
	userRepo      repo.UserRepo
	userVipRepo   repo.UserVipRepo
	antsPool      *ants.Pool
}

func NewUserLoginService(
	bootStrap *component.BootStrap,
	userRepo repo.UserRepo,
	configService *service2.ConfigService,
	userVipRepo repo.UserVipRepo,
) *UserLoginService {
	return &UserLoginService{
		log:           bootStrap.Log,
		tx:            bootStrap.Tx,
		userRepo:      userRepo,
		configService: configService,
		antsPool:      bootStrap.GoroutinePool,
		userVipRepo:   userVipRepo,
	}
}

// OneClickLogin 一键登录
func (s *UserLoginService) OneClickLogin(req *dto.UserLoginRequest) (*dto.UserLoginResponse, errpkg.IError) {
	// 调用一键登录，获取手机号
	phone, _err := getui.OneClickLogin(req.Token, req.Gyuid)
	if _err != nil {
		s.log.Error("个推服务异常: %v", _err)
		return nil, errpkg.NewMiddleError(response.GeTuiServiceError)
	}

	// 判断该 phone 关联的用户是否已被注销
	if isCancel, err := s.userRepo.IsCancelUser("phone", phone); err != nil {
		return nil, errpkg.NewHighError(response.DbError)
	} else if isCancel {
		return nil, errpkg.NewLowError(response.UserCancelError)
	}

	// 根据该 phone 判断是否为新用户
	if isNew, err := s.userRepo.IsNewUser("phone", phone); err != nil {
		return nil, errpkg.NewHighError(response.DbError)
	} else if isNew {
		// 是新用户，注册
		if err := s.register(req, constant.OneClickLogin, phone); err != nil {
			return nil, err
		}
	}

	// 不是新用户/已完成注册 查询信息
	user, err := s.userRepo.GetUserInfoByField("phone", phone)
	if err != nil {
		return nil, errpkg.NewHighError(response.DbError)
	}

	resp := &dto.UserLoginResponse{
		UserInfo: &dto.UserInfo{
			ID:       user.ID,
			Avatar:   user.Avatar,
			Username: user.Username,
			Diamond:  user.Diamond,
			Phone:    s.handlePhone(user.Phone),
			IsVip:    user.IsVip,
		},
	}

	// 如果是会员，查询会员信息
	if user.IsVip == 1 {
		if err := s.addUserVipInfo2Resp(user.ID, resp); err != nil {
			s.log.Error("addUserVipInfo2Resp err: %v", err)
			return nil, err
		}
	}

	// 生成 token
	if resp.Token, err = s.generateToken(map[string]any{
		"user_id": user.ID,
	}); err != nil {
		return nil, errpkg.NewMiddleError(response.TokenGenerateError)
	}
	return resp, nil
}

func (s *UserLoginService) addUserVipInfo2Resp(userId int64, resp *dto.UserLoginResponse) errpkg.IError {
	vipInfo, err := s.userVipRepo.GetUserVipByUserId(userId)
	if err != nil {
		return errpkg.NewHighError(response.DbError)
	}

	if vipInfo.ID != 0 {
		resp.UserInfo.VipInfo = &dto.UserVipInfo{
			VipType:   vipInfo.VipType,
			CreateAt:  vipInfo.CreateAt,
			RenewalAt: vipInfo.RenewalAt,
			ExpireAt:  vipInfo.ExpireAt,
			IsExpire:  vipInfo.IsExpire,
		}
		return nil
	}

	return nil
}

// SendCode 发送验证码
func (s *UserLoginService) SendCode(req *dto.UserSendCodeRequest) (string, errpkg.IError) {
	// 特权账号
	if req.Phone == constant.PrivilegePhone1 || req.Phone == constant.PrivilegePhone2 {
		if _err := s.userRepo.SetSmsCode(req.Phone, constant.PrivilegeCode); _err != nil {
			return "", errpkg.NewHighError(response.RedisError)
		}
		return "OK", nil
	}

	// 准备SMS code
	smsCode := utils.GenValidateCode(4)

	// 开发环境下，直接使用验证码
	if apollo.GetApolloConfig().Env == "dev" {
		// 保存验证码到 redis
		if _err := s.userRepo.SetSmsCode(req.Phone, smsCode); _err != nil {
			return "", errpkg.NewHighError(response.RedisError)
		}
		return smsCode, nil
	}

	// 判断是否在冷却时间内
	cooling, err := s.userRepo.GetSendCooling(req.Phone)
	if err != nil {
		return "", errpkg.NewHighError("获取发送冷却时间出错")
	}
	if cooling {
		return "", errpkg.NewLowError("短信发送冷却中，请勿频繁请求")
	} else {
		if err := s.userRepo.SetSendCooling(req.Phone); err != nil {
			return "", errpkg.NewHighError("设置发送冷却时间出错")
		}
	}

	// 判断该 phone 关联的用户是否已被注销
	if isCancel, err := s.userRepo.IsCancelUser("phone", req.Phone); err != nil {
		return "", errpkg.NewHighError(response.DbError)
	} else if isCancel {
		return "", errpkg.NewLowError(response.UserCancelError)
	}

	// 发送验证码
	if err := sms.SendSmsV2(req.Phone, smsCode); err != nil {
		return "", errpkg.NewLowError(response.SendCodeError)
	}

	// 保存验证码到 redis
	if err := s.userRepo.SetSmsCode(req.Phone, smsCode); err != nil {
		return "", errpkg.NewHighError(response.RedisError)
	}

	return "OK", nil
}

// PhoneCodeLogin 手机验证码登录
func (s *UserLoginService) PhoneCodeLogin(req *dto.UserLoginRequest) (*dto.UserLoginResponse, errpkg.IError) {
	// 判断该 phone 关联的用户是否已被注销
	if isCancel, err := s.userRepo.IsCancelUser("phone", req.Phone); err != nil {
		return nil, errpkg.NewHighError(response.DbError)
	} else if isCancel {
		return nil, errpkg.NewLowError(response.UserCancelError)
	}

	// 获取手机错误次数
	count, err := s.userRepo.GetPhoneError(req.Phone)
	if err != nil {
		s.log.Error("判断验证码错误次数出错: %v", err)
		return nil, errpkg.NewHighError("判断验证码错误次数出错")
	}

	// 验证码错误次数超过5次，登录失败
	if count >= 5 {
		return nil, errpkg.NewLowError("验证码错误次数过多，请10分钟再试")
	}

	// 取出保存的验证码
	smsCode, err := s.userRepo.GetSmsCode(req.Phone)
	if err != nil {
		return nil, errpkg.NewHighError("获取缓存验证码失败")
	}

	// 验证码错误，登录失败
	if req.Code != smsCode {
		if err := s.userRepo.SetPhoneError(req.Phone); err != nil {
			return nil, errpkg.NewHighError("设置手机错误次数出错")
		}
		return nil, errpkg.NewLowError(fmt.Sprintf("验证码错误,还有 %v 次机会", 5-count))
	}

	// 根据手机号判断是否为新用户
	if isNew, err := s.userRepo.IsNewUser("phone", req.Phone); err != nil {
		return nil, errpkg.NewHighError(response.DbError)
	} else if isNew {
		// 是新用户，注册
		if err := s.register(req, constant.PhoneCodeLogin, req.Phone); err != nil {
			return nil, err
		}
	}

	// 不是新用户/已完成注册 查询信息
	user, err := s.userRepo.GetUserInfoByField("phone", req.Phone)
	if err != nil {
		return nil, errpkg.NewHighError(response.DbError)
	}

	resp := &dto.UserLoginResponse{
		UserInfo: &dto.UserInfo{
			ID:       user.ID,
			Avatar:   user.Avatar,
			Username: user.Username,
			Diamond:  user.Diamond,
			Phone:    s.handlePhone(user.Phone),
			IsVip:    user.IsVip,
		},
	}

	// 如果是会员，查询会员信息
	if user.IsVip == 1 {
		if err := s.addUserVipInfo2Resp(user.ID, resp); err != nil {
			s.log.Error("addUserVipInfo2Resp err: %v", err)
			return nil, err
		}
	}

	// 生成 token
	if resp.Token, err = s.generateToken(map[string]any{
		"user_id": user.ID,
	}); err != nil {
		return nil, errpkg.NewMiddleError(response.TokenGenerateError)
	}
	return resp, nil
}

// AppleLogin 苹果登录（游客登录）
func (s *UserLoginService) AppleLogin(req *dto.UserLoginRequest) (*dto.UserLoginResponse, errpkg.IError) {
	// 判断该设备号关联的用户是否已被注销
	if isCancel, err := s.userRepo.IsCancelUser("device_id", req.DeviceId); err != nil {
		return nil, errpkg.NewHighError(response.DbError)
	} else if isCancel {
		return nil, errpkg.NewLowError(response.UserCancelError)
	}

	// 根据 deviceId 判断是否为新用户
	if isNew, err := s.userRepo.IsNewUser("device_id", req.DeviceId); err != nil {
		return nil, errpkg.NewHighError(response.DbError)
	} else if isNew {
		// 是新用户，注册
		if err := s.register(req, constant.AppleLogin); err != nil {
			return nil, err
		}
	}

	// 不是新用户/已完成注册 返回信息
	visitor, err := s.userRepo.GetUserInfoByField("device_id", req.DeviceId)
	if err != nil {
		return nil, errpkg.NewHighError(response.DbError)
	}

	resp := &dto.UserLoginResponse{
		UserInfo: &dto.UserInfo{
			ID:       visitor.ID,
			Avatar:   visitor.Avatar,
			Username: visitor.Username,
			Diamond:  visitor.Diamond,
			Phone:    s.handlePhone(visitor.Phone),
			IsVip:    visitor.IsVip,
		},
	}

	// 如果是会员，查询会员信息
	if visitor.IsVip == 1 {
		if err := s.addUserVipInfo2Resp(visitor.ID, resp); err != nil {
			s.log.Error("addUserVipInfo2Resp err: %v", err)
			return nil, err
		}
	}

	// 生成 token
	if resp.Token, err = s.generateToken(map[string]any{
		"user_id": visitor.ID,
	}); err != nil {
		return nil, errpkg.NewMiddleError(response.TokenGenerateError)
	}
	return resp, nil
}

// 手机号处理，将中间几位用*代替
func (s *UserLoginService) handlePhone(phone string) string {
	if phone != "" && len(phone) == 11 {
		return phone[:3] + "****" + phone[7:]
	} else {
		return "暂未绑定手机号"
	}
}

func (s *UserLoginService) generateToken(meta map[string]any) (token string, err error) {
	if token, err = jwt.GenerateToken(apollo.GetApolloConfig().JwtSecret, time.Hour*24*30*6, meta); err != nil {
		return "", err
	}
	return token, nil
}

func (s *UserLoginService) register(req *dto.UserLoginRequest, registerType int64, phone ...string) errpkg.IError {
	// 根据 device_id 查询该新用户之前是否是游客登录过
	visitor, err := s.userRepo.GetUserInfoByDeviceId(req.DeviceId)
	if err != nil {
		return errpkg.NewHighError(response.DbError)
	}

	// 没有该设备id注册过的游客信息
	if visitor.ID == 0 {
		user := &dto.UserInfoDto{
			DeviceId:        req.DeviceId,
			Channel:         req.Channel,
			RegisterType:    registerType,
			RegisterVersion: req.Version,
			Ip:              req.Ip,
		}
		switch req.Type {
		case 1, 2:
			// 手机号注册
			user.Phone = phone[0]
		}

		// 从配置获取默认头像配置
		defaultAvatarConfig, err := s.configService.GetConfigByKeyFromCache(context.Background(), "default_avatar")
		if err != nil {
			return errpkg.NewHighError(response.DbError)
		}

		// 将配置解析为头像 list
		var avatarList []string
		if err := json.Unmarshal([]byte(defaultAvatarConfig), &avatarList); err != nil {
			s.log.Error("解析默认头像配置失败: %v", err)
			return errpkg.NewHighError(response.ConfigNotExit)
		}

		// 随机选择一个头像
		user.Avatar = avatarList[rand.Intn(len(avatarList))]

		// 保存用户信息
		if err := s.userRepo.CreateUser(user); err != nil {
			return errpkg.NewHighError(response.DbError)
		}

		// 将主键转为 username
		if err := s.userRepo.UpdateUsername(user.ID, "用户"+strconv.FormatInt(user.ID, 10)); err != nil {
			return errpkg.NewHighError(response.DbError)
		}

		// 开启协程将ip转为地理位置
		s.submitTask2Pool(func() {
			s.updateIpLocation(user, req)
		})
	} else {
		// 有该设备id注册过的游客信息
		switch req.Type {
		case 1, 2:
			// 手机号注册
			if len(phone) > 0 {
				return s.phoneRegister(visitor, req, phone[0], registerType)
			} else {
				return s.phoneRegister(visitor, req, req.Phone, registerType)
			}
		}
	}

	return nil
}

func (s *UserLoginService) phoneRegister(visitor *dto.UserInfoDto, req *dto.UserLoginRequest, phone string, registerType int64) errpkg.IError {
	// 该游客没有手机号
	if visitor.Phone == "" {
		if err := s.userRepo.UpdateUserInfoByDeviceId(req.DeviceId, map[string]any{"phone": phone}); err != nil {
			return errpkg.NewHighError(response.DbError)
		}
	} else {
		// 该游客有手机号，将该手机号注册新用户
		user := &dto.UserInfoDto{
			Channel:      req.Channel,
			Phone:        phone,
			RegisterType: registerType,
		}

		user := &dto.UserInfoDto{
			DeviceId:        req.DeviceId,
			Phone:           phone,
			Channel:         req.Channel,
			RegisterType:    registerType,
			RegisterVersion: req.Version,
			Ip:              req.Ip,
		}

		if err := s.userRepo.CreateUser(user); err != nil {
			return errpkg.NewHighError(response.DbError)
		}

		// 将主键转为 username
		if err := s.userRepo.UpdateUsername(user.ID, "用户"+strconv.FormatInt(user.ID, 10)); err != nil {
			return errpkg.NewHighError(response.DbError)
		}

		// 开启协程将ip转为地理位置
		s.submitTask2Pool(func() {
			s.updateIpLocation(user, req)
		})
	}
	return nil
}

func (s *UserLoginService) submitTask2Pool(task func()) {
	// 提交任务到协程池
	err := s.antsPool.Submit(task)
	if err != nil {
		s.log.Error("提交任务到协程池出错: %v", err.Error())
	}
}

func (s *UserLoginService) updateIpLocation(user *dto.UserInfoDto, req *dto.UserLoginRequest) {
	if location, err := ip.GetIPLocationV2(req.Ip); err == nil && location != "" {
		if err := s.userRepo.UpdateUserInfoByUserId(user.ID, map[string]any{
			"ip_location": location,
		}); err != nil {
			s.log.Error("更新用户ip_location失败: %v, id:%d", err, user.ID)
		}
	} else {
		s.log.Info("没有成功解析出ip_location: %v, id:%d", err, user.ID)
	}
}
