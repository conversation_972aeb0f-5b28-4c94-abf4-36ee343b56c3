package processors

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/pkg/volcengine"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/pkg/getui"
	"chongli/pkg/logger"
	"chongli/pkg/qiniu"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"runtime"
	"time"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	v20240523 "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vclm/v20240523"
)

type AvatarPictureProcessors struct {
	bootstrap       *component.BootStrap
	taskService     *service.TaskService
	userWorkService *service.UserWorkService
	log             *logger.Logger
	userService     *service.UserService
}

func NewAvatarPictureProcessors(
	bootstrap *component.BootStrap,
	taskService *service.TaskService,
	userWorkService *service.UserWorkService,
	userService *service.UserService,
) *AvatarPictureProcessors {
	return &AvatarPictureProcessors{
		bootstrap:       bootstrap,
		taskService:     taskService,
		userWorkService: userWorkService,
		log:             bootstrap.Log,
		userService:     userService,
	}
}

func (s *AvatarPictureProcessors) PushRoleTask() {
	ctx := context.Background()

	step, err := s.taskService.GetPendingTaskStep(ctx, model.StepNameCreateRole, nil)
	if err != nil {
		s.log.Error("获取待处理的步骤失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("没有待处理的步骤：%v", model.StepNameCreateRole)
		return
	}

	// 验证参数
	params := step.Params
	petPic, ok := params["pet_pic"].(string)
	if !ok || petPic == "" {
		s.log.Error("步骤ID: %d - pet_pic参数缺失或格式错误", step.ID)
		step.ErrorMsg = "pet_pic参数缺失或格式错误"
		s.handleStepError(ctx, step, "参数错误")
		return
	}

	// 创建火山引擎客户端
	volcClient := volcengine.NewClient(&volcengine.Config{
		AccessKey: s.bootstrap.Config.VolcengineAccessKey,
		SecretKey: s.bootstrap.Config.VolcengineSecretKey,
	})

	// 构建角色创建任务请求
	volcReq := volcengine.NewSubmitTaskRequest(map[string]interface{}{
		"req_key":   volcengine.RealmanAvatarPictureCreateRoleLoopy,
		"image_url": petPic,
	})

	// 提交任务
	volcResp, code, err := volcClient.CVSubmitTask(volcReq)
	if code == 429 {
		s.log.Error("步骤ID: %d - 超过频率限制请等待: %d", step.ID, code)
		return
	}
	if err != nil {
		s.log.Error("步骤ID: %d - 提交火山引擎角色创建任务失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "任务提交失败")
		return
	}

	if volcResp.Error != "" {
		s.log.Error("步骤ID: %d - 角色创建任务返回错误: %s", step.ID, volcResp.Error)
		step.ErrorMsg = fmt.Sprintf("角色创建任务返回错误: %s", volcResp.Error)
		s.handleStepError(ctx, step, "任务提交失败")
		return
	}

	// 将task_id写入result并更新状态为waiting_result
	resultJSON, err := json.Marshal(map[string]interface{}{
		"task_id": volcResp.Data.TaskID,
	})
	if err != nil {
		s.log.Error("步骤ID: %d - 序列化结果数据失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "数据序列化失败")
		return
	}

	err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusWaitingResult, resultJSON)
	if err != nil {
		s.log.Error("步骤ID: %d - 更新步骤状态失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "状态更新失败")
		return
	}

	s.log.Info("步骤ID: %d - 角色创建任务已提交，task_id: %s", step.ID, volcResp.Data.TaskID)
}

func (s *AvatarPictureProcessors) GetRoleTask() {
	ctx := context.Background()

	// 1. 获取状态为waiting_result，步骤名为create_role的任务
	step, err := s.getWaitingResultStep(ctx, model.StepNameCreateRole)
	if err != nil {
		s.log.Error("获取waiting_result状态的create_role任务失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("没有waiting_result状态的create_role任务")
		return
	}

	s.log.Info("步骤ID: %d - 开始处理角色创建任务结果获取", step.ID)

	// 2. 解析result字段中的task_id
	taskID, err := s.extractTaskIDFromResult(step.Result)
	if err != nil {
		s.log.Error("步骤ID: %d - 解析task_id失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "任务ID解析失败")
		return
	}

	// 3. 创建火山引擎客户端
	volcClient := volcengine.NewClient(&volcengine.Config{
		AccessKey: s.bootstrap.Config.VolcengineAccessKey,
		SecretKey: s.bootstrap.Config.VolcengineSecretKey,
	})

	// 4. 调用CVSync2AsyncGetResult获取结果
	getResultReq := &volcengine.CVSync2AsyncGetResultRequest{
		ReqKey: volcengine.RealmanAvatarPictureCreateRoleLoopy,
		TaskID: taskID,
	}

	volcResp, err := volcClient.CVGetResult(getResultReq)
	if err != nil {
		s.log.Error("步骤ID: %d - 获取火山引擎角色创建任务结果失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "任务结果获取失败")
		s.log.Info("步骤ID: %d - 角色创建任务 %s 仍在处理中，继续等待", step.ID, taskID)
		return
	}
	if volcResp != nil && volcResp.Data.RespData != "" {

		// 6. 解析RespData JSON字符串获取resource_id
		var respData map[string]any
		if err := json.Unmarshal([]byte(volcResp.Data.RespData), &respData); err != nil {
			s.log.Error("步骤ID: %d - 解析RespData失败: %v", step.ID, err)
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, "数据解析失败")
			return
		}

		resourceID, ok := respData["resource_id"].(string)
		if !ok || resourceID == "" {
			s.log.Error("步骤ID: %d - 未能从RespData中获取到resource_id", step.ID)
			step.ErrorMsg = "未能从RespData中获取到resource_id"
			s.handleStepError(ctx, step, "数据获取失败")
			return
		}

		// 获取下一步
		nextSteps, err := s.taskService.GetStepQuery(ctx, &dto.GetStepQuery{
			Step: &dto.TaskStepDTO{
				StepIndex: step.StepIndex + 1,
				WorkID:    step.WorkID,
			},
		})
		if err != nil {
			s.log.Error("步骤ID: %d - 获取下一步失败: %v", step.ID, err)
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, "下一步获取失败")
			return
		}
		if nextSteps == nil {
			s.log.Info("步骤ID: %d - 任务 %d 已完成", step.ID, step.WorkID)
			return
		}
		if len(nextSteps) == 0 {
			s.log.Error("步骤ID: %d - 获取下一步失败: 获取到的下一步是0个", step.ID)
			return
		}
		nextStep := nextSteps[0]
		// 检查下一步参数
		if nextStep.Params == nil {
			s.log.Error("步骤ID: %d - 下一步参数为空", nextStep.ID)
			nextStep.ErrorMsg = "下一步参数为空"
			s.handleStepError(ctx, nextStep, "参数缺失")
			return
		}
		audioURL, ok := nextStep.Params["audio_url"]
		if !ok {
			s.log.Error("步骤ID: %d - 下一步参数中缺少audio_url", nextStep.ID)
			nextStep.ErrorMsg = "下一步参数中缺少audio_url"
			s.handleStepError(ctx, nextStep, "参数缺失")
			return
		}
		audioURLStr, ok := audioURL.(string)
		if !ok {
			s.log.Error("步骤ID: %d - audio_url参数类型错误，应为string", nextStep.ID)
			nextStep.ErrorMsg = "audio_url参数类型错误，应为string"
			s.handleStepError(ctx, nextStep, "参数类型错误")
			return
		}
		avatarResp, code, err := volcClient.CVSubmitTask(&volcengine.CVSync2AsyncSubmitTaskRequest{
			ReqBody: map[string]any{
				"req_key":     volcengine.RealmanAvatarPictureLoopy,
				"resource_id": resourceID,
				"audio_url":   audioURLStr,
			},
		})
		if code == 429 {
			s.log.Error("步骤ID: %d - 超过频率限制请等待: %d", nextStep.ID, code)
			return
		}
		if err != nil {
			s.log.Error("步骤ID: %d - 生成对口型失败: %v", nextStep.ID, err)
			nextStep.ErrorMsg = err.Error()
			s.handleStepError(ctx, nextStep, "对口型生成失败")
			return
		}
		if code != 200 {
			s.log.Error("步骤ID: %d - 生成对口型失败: %d", nextStep.ID, code)
			nextStep.ErrorMsg = fmt.Sprintf("生成对口型失败: %d", code)
			s.handleStepError(ctx, nextStep, "对口型生成失败")
			return
		}
		if avatarResp.Error != "" {
			s.log.Error("步骤ID: %d - 生成对口型失败: %s", nextStep.ID, avatarResp.Error)
			nextStep.ErrorMsg = fmt.Sprintf("生成对口型失败: %s", avatarResp.Error)
			s.handleStepError(ctx, nextStep, "对口型生成失败")
			return
		}
		// 将上一步任务完成
		resultJSON, _ := json.Marshal(map[string]any{
			"task_id":     taskID,
			"resource_id": resourceID,
		})
		err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusDone, resultJSON)
		if err != nil {
			s.log.Error("步骤ID: %d - 更新步骤状态失败: %v", step.ID, err)
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, "状态更新失败")
			return
		}
		// 将task_id写入result并更新状态为waiting_result
		avatarResultJSON, err := json.Marshal(map[string]any{
			"task_id": avatarResp.Data.TaskID,
		})
		if err != nil {
			s.log.Error("步骤ID: %d - 序列化结果数据失败: %v", nextStep.ID, err)
			nextStep.ErrorMsg = err.Error()
			s.handleStepError(ctx, nextStep, "数据序列化失败")
			return
		}
		err = s.taskService.UpdateStepStatusAndResult(ctx, nextStep, model.StepStatusWaitingResult, avatarResultJSON)
		if err != nil {
			s.log.Error("步骤ID: %d - 更新步骤状态失败: %v", nextStep.ID, err)
			nextStep.ErrorMsg = err.Error()
			s.handleStepError(ctx, nextStep, "状态更新失败")
			return
		}
		s.log.Info("步骤ID: %d - 视频生成任务已提交，task_id: %s", nextStep.ID, avatarResp.Data.TaskID)
	} else {
		s.log.Info("步骤ID: %d - 视频生成任务 %s 仍在处理中，继续等待", step.ID, taskID)
	}
}

func (s *AvatarPictureProcessors) GetAvatarPictureTask() {
	ctx := context.Background()

	// 1. 获取状态为waiting_result，步骤名为create_role的任务
	step, err := s.getWaitingResultStep(ctx, model.StepNameAvatarPicture)
	if err != nil {
		s.log.Error("获取waiting_result状态的create_role任务失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("没有waiting_result状态的create_role任务")
		return
	}

	s.log.Info("步骤ID: %d - 开始处理角色创建任务结果获取", step.ID)

	// 2. 解析result字段中的task_id
	taskID, err := s.extractTaskIDFromResult(step.Result)
	if err != nil {
		s.log.Error("步骤ID: %d - 解析task_id失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "任务ID解析失败")
		return
	}

	// 3. 创建火山引擎客户端
	volcClient := volcengine.NewClient(&volcengine.Config{
		AccessKey: s.bootstrap.Config.VolcengineAccessKey,
		SecretKey: s.bootstrap.Config.VolcengineSecretKey,
	})

	// 4. 调用CVSync2AsyncGetResult获取结果
	getResultReq := &volcengine.CVSync2AsyncGetResultRequest{
		ReqKey: volcengine.RealmanAvatarPictureCreateRoleLoopy,
		TaskID: taskID,
	}

	volcResp, err := volcClient.CVGetResult(getResultReq)
	if err != nil {
		s.log.Error("步骤ID: %d - 获取火山引擎角色创建任务结果失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "任务结果获取失败")
		return
	}

	if volcResp == nil {
		s.log.Info("步骤ID: %d - 生成对口型视频 %s 仍在处理中，继续等待", step.ID, taskID)
		return
	}

	if volcResp.Error != "" {
		s.log.Error("步骤ID: %d - 生成对口型视频 %s 返回错误: %s", step.ID, taskID, volcResp.Error)
		step.ErrorMsg = fmt.Sprintf("生成对口型视频 %s 返回错误: %s", taskID, volcResp.Error)
		s.handleStepError(ctx, step, "视频生成失败")
		return
	}

	// 6. 解析RespData JSON字符串获取结果
	var respData map[string]any
	if err := json.Unmarshal([]byte(volcResp.Data.RespData), &respData); err != nil {
		s.log.Error("步骤ID: %d - 解析RespData失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "数据解析失败")
		return
	}

	// 7. 检查code是否为0（成功）
	code, ok := respData["code"].(float64)
	if !ok {
		s.log.Error("步骤ID: %d - 未能从RespData中获取到code字段", step.ID)
		step.ErrorMsg = "未能从RespData中获取到code字段"
		s.handleStepError(ctx, step, "数据获取失败")
		return
	}

	if code != 0 {
		msg, _ := respData["msg"].(string)
		s.log.Error("步骤ID: %d - 视频生成任务失败，code: %.0f, msg: %s", step.ID, code, msg)
		step.ErrorMsg = fmt.Sprintf("视频生成任务失败，code: %.0f, msg: %s", code, msg)
		s.handleStepError(ctx, step, "视频生成失败")
		return
	}

	// 8. 获取preview_url数组的第一个值
	previewURLs, ok := respData["preview_url"].([]interface{})
	if !ok || len(previewURLs) == 0 {
		s.log.Error("步骤ID: %d - 未能从RespData中获取到preview_url或数组为空", step.ID)
		step.ErrorMsg = "未能从RespData中获取到preview_url或数组为空"
		s.handleStepError(ctx, step, "数据获取失败")
		return
	}

	previewURL, ok := previewURLs[0].(string)
	if !ok || previewURL == "" {
		s.log.Error("步骤ID: %d - preview_url的第一个值不是有效的字符串", step.ID)
		step.ErrorMsg = "preview_url的第一个值不是有效的字符串"
		s.handleStepError(ctx, step, "数据格式错误")
		return
	}

	// 9. 更新步骤状态为完成，并将preview_url存入result
	resultJSON, err := json.Marshal(map[string]any{
		"task_id":     taskID,
		"preview_url": previewURL,
		"code":        code,
		"msg":         respData["msg"],
	})
	if err != nil {
		s.log.Error("步骤ID: %d - 序列化结果数据失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "数据序列化失败")
		return
	}

	// 将视频文件从预览URL抓取到七牛云存储
	// 生成文件名（使用步骤ID和task_id）
	filename := fmt.Sprintf("avatar_video_%d_%s.mp4", step.ID, taskID)
	// 按照指定规则生成目标文件地址
	qiniuPath := fmt.Sprintf("%s/%s", time.Now().Format("2006/01/02"), filename)
	qiniuURL, err := qiniu.FetchFile(previewURL, qiniuPath)
	if err != nil {
		s.log.Error("步骤ID: %d - 抓取视频文件到七牛云失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "视频转存失败")
		// 这里不直接返回错误，而是使用原始URL
		qiniuURL = previewURL
	} else {
		s.log.Info("步骤ID: %d - 视频文件已抓取到七牛云: %s", step.ID, qiniuURL)
	}
	err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusDone, resultJSON)
	if err != nil {
		s.log.Error("步骤ID: %d - 更新步骤状态失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "状态更新失败")
		return
	}
	// err = s.taskService.CompleteTask(ctx, &step.Task, "", nil)
	// if err != nil {
	// 	s.log.Error("步骤ID: %d - 完成任务失败: %v", step.ID, err)
	// 	s.handleStepError(ctx, step, fmt.Sprintf("完成任务失败: %v", err))
	// 	return
	// }

	err = s.userWorkService.DoneUserWork(ctx, step.WorkID, map[string]any{
		"video_url": qiniuURL,
	}, nil)
	if err != nil {
		s.log.Error("步骤ID: %d - 完成用户作品失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "作品完成失败")
		return
	}
	s.log.Info("步骤ID: %d - 视频生成任务完成，preview_url: %s, qiniu_url: %s", step.ID, previewURL, qiniuURL)

}

// getWaitingResultStep 获取状态为waiting_result且步骤名为指定名称的任务步骤
func (s *AvatarPictureProcessors) getWaitingResultStep(ctx context.Context, stepName string) (*dto.TaskStepDTO, error) {
	limit := 1
	query := &dto.GetStepQuery{
		Step: &dto.TaskStepDTO{
			Status:   model.StepStatusWaitingResult,
			StepName: stepName,
		},
		Limit: &limit,
	}

	stepDTOs, err := s.taskService.GetStepQuery(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("获取waiting_result状态的步骤失败: %w", err)
	}

	if len(stepDTOs) == 0 {
		return nil, nil // 没有找到对应的步骤
	}

	return stepDTOs[0], nil
}

// extractTaskIDFromResult 从result字段中解析task_id
func (s *AvatarPictureProcessors) extractTaskIDFromResult(result dto.JSONMap) (string, error) {
	if result == nil {
		return "", fmt.Errorf("result字段为空")
	}

	taskID, ok := result["task_id"].(string)
	if !ok {
		return "", fmt.Errorf("result中没有找到task_id或类型错误")
	}

	if taskID == "" {
		return "", fmt.Errorf("task_id为空")
	}

	return taskID, nil
}

// handleStepError 统一处理步骤失败的错误
func (s *AvatarPictureProcessors) handleStepError(ctx context.Context, step *dto.TaskStepDTO, errorMsg string) {
	// 获取调用者的文件名和行号
	_, file, line, ok := runtime.Caller(1)
	if ok {
		// 只保留文件名，不包含完整路径
		filename := filepath.Base(file)
		// 在ErrorMsg中添加调用位置信息
		if step.ErrorMsg != "" {
			step.ErrorMsg = fmt.Sprintf("%s [%s:%d]", step.ErrorMsg, filename, line)
		} else {
			step.ErrorMsg = fmt.Sprintf("错误位置: %s:%d", filename, line)
		}
	}

	s.log.Error("步骤ID: %d - %s", step.ID, errorMsg)
	step.Status = model.StepStatusFailed
	err := s.taskService.StepFail(ctx, step, errors.New(errorMsg))
	if err != nil {
		s.log.Error("步骤ID: %d - 标记步骤失败时出错: %v", step.ID, err)
	}
	//if err := s.pushGetui(step, false); err != nil {
	//	s.log.Error("步骤ID: %d - 推送个推失败: %v", step.ID, err)
	//}
}

// completeTaskAndCreateUserWork 完成任务并创建用户作品
func (s *AvatarPictureProcessors) completeTaskAndCreateUserWork(ctx context.Context, step *dto.TaskStepDTO, finalVideoURL string) error {

	// 如果存在UserWorkID，则更新用户作品状态
	if step.WorkID > 0 {
		updates := map[string]any{
			"video_url": finalVideoURL,
		}

		// 使用已封装的用户作品服务完成用户作品
		err := s.userWorkService.DoneUserWork(ctx, step.WorkID, updates)
		if err != nil {
			return fmt.Errorf("更新用户作品失败: %w", err)
		}

		s.log.Info("[用户作品] 任务ID:%d 步骤ID:%d 用户作品已完成，WorkID: %d, 视频URL: %s", step.WorkID, step.ID, step.WorkID, finalVideoURL)
	}

	return nil
}

// SubmitTencentPortraitSingJob 提交腾讯混元对口型任务
func (s *AvatarPictureProcessors) SubmitTencentPortraitSingJob() {
	ctx := context.Background()

	// 获取待处理的对口型任务步骤
	step, err := s.taskService.GetPendingTaskStep(ctx, model.StepNameAvatarPicture, nil)
	if err != nil {
		s.log.Error("获取待处理的对口型步骤失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("没有待处理的对口型步骤：%v", model.StepNameAvatarPicture)
		return
	}

	// 验证参数
	params := step.Params
	petPic, ok := params["pet_pic"].(string)
	if !ok || petPic == "" {
		s.log.Error("步骤ID: %d - pet_pic参数缺失或格式错误", step.ID)
		step.ErrorMsg = "pet_pic参数缺失或格式错误"
		s.handleStepError(ctx, step, "参数错误")
		return
	}

	audioURL, ok := params["audio_url"].(string)
	if !ok || audioURL == "" {
		s.log.Error("步骤ID: %d - audio_url参数缺失或格式错误", step.ID)
		step.ErrorMsg = "audio_url参数缺失或格式错误"
		s.handleStepError(ctx, step, "参数错误")
		return
	}

	// 创建腾讯云客户端
	cred := common.NewCredential(
		s.bootstrap.Config.TencentSecretId,
		s.bootstrap.Config.TencentSecretKey,
	)

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "vclm.tencentcloudapi.com"

	client, err := v20240523.NewClient(cred, s.bootstrap.Config.TencentRegion, cpf)
	if err != nil {
		s.log.Error("步骤ID: %d - 创建腾讯云客户端失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "客户端创建失败")
		return
	}

	// 获取用户信息
	userId := step.UserWork.UserID
	user, err := s.userService.GetUserInfoByUid(int64(userId))
	if err != nil {
		s.log.Error("步骤ID: %d - 获取用户信息失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "用户信息获取失败")
		return
	}

	// 构建提交对口型任务请求
	request := v20240523.NewSubmitPortraitSingJobRequest()
	request.AudioUrl = common.StringPtr(audioURL)
	request.ImageUrl = common.StringPtr(petPic)
	request.Mode = common.StringPtr("Pet") // 宠物模式，支持宠物等非人像图片
	if user.IsVip == model.IsVip {
		request.LogoAdd = common.Int64Ptr(0) // 不添加AI生成标识
	} else {
		request.LogoAdd = common.Int64Ptr(1) // 添加AI生成标识
	}

	// 提交任务
	response, err := client.SubmitPortraitSingJob(request)
	if err != nil {
		s.log.Error("步骤ID: %d - 提交腾讯混元对口型任务失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "对口型任务提交失败")
		return
	}

	if response.Response.JobId == nil {
		s.log.Error("步骤ID: %d - 腾讯混元对口型任务返回JobId为空", step.ID)
		step.ErrorMsg = "腾讯混元对口型任务返回JobId为空"
		s.handleStepError(ctx, step, "任务提交失败")
		return
	}

	jobId := *response.Response.JobId

	// 将job_id写入result并更新状态为waiting_result
	resultJSON, err := json.Marshal(map[string]any{
		"job_id": jobId,
	})
	if err != nil {
		s.log.Error("步骤ID: %d - 序列化结果数据失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "数据序列化失败")
		return
	}

	err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusWaitingResult, resultJSON)
	if err != nil {
		s.log.Error("步骤ID: %d - 更新步骤状态失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "状态更新失败")
		return
	}

	s.log.Info("步骤ID: %d - 腾讯混元对口型任务已提交，job_id: %s", step.ID, jobId)
}

// GetTencentPortraitSingResult 获取腾讯混元对口型任务结果
func (s *AvatarPictureProcessors) GetTencentPortraitSingResult() {
	ctx := context.Background()

	// 获取状态为waiting_result，步骤名为avatar_picture的任务
	step, err := s.getWaitingResultStep(ctx, model.StepNameAvatarPicture)
	if err != nil {
		s.log.Error("获取waiting_result状态的avatar_picture任务失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("没有waiting_result状态的avatar_picture任务")
		return
	}

	s.log.Info("步骤ID: %d - 开始处理腾讯混元对口型任务结果获取", step.ID)

	// 解析result字段中的job_id
	jobID, err := s.extractJobIDFromResult(step.Result)
	if err != nil {
		s.log.Error("步骤ID: %d - 解析job_id失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "任务ID解析失败")
		return
	}

	// 创建腾讯云客户端
	cred := common.NewCredential(
		s.bootstrap.Config.TencentSecretId,
		s.bootstrap.Config.TencentSecretKey,
	)

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "vclm.tencentcloudapi.com"

	client, err := v20240523.NewClient(cred, s.bootstrap.Config.TencentRegion, cpf)
	if err != nil {
		s.log.Error("步骤ID: %d - 创建腾讯云客户端失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "客户端创建失败")
		return
	}

	// 调用DescribePortraitSingJob获取结果
	request := v20240523.NewDescribePortraitSingJobRequest()
	request.JobId = common.StringPtr(jobID)

	response, err := client.DescribePortraitSingJob(request)
	if err != nil {
		s.log.Error("步骤ID: %d - 获取腾讯混元对口型任务结果失败: %v", step.ID, err)
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "任务结果获取失败")
		return
	}

	if response.Response.StatusCode == nil {
		s.log.Info("步骤ID: %d - 腾讯混元对口型任务 %s 仍在处理中，继续等待", step.ID, jobID)
		return
	}

	statusCode := *response.Response.StatusCode
	s.log.Info("步骤ID: %d - 腾讯混元对口型任务状态: %s", step.ID, statusCode)

	// 检查任务状态
	switch statusCode {
	case "DONE":
		// 任务完成，处理结果
		if response.Response.ResultVideoUrl == nil {
			s.log.Error("步骤ID: %d - 腾讯混元对口型任务完成但ResultVideoUrl为空", step.ID)
			step.ErrorMsg = "腾讯混元对口型任务完成但ResultVideoUrl为空"
			s.handleStepError(ctx, step, "视频获取失败")
			return
		}

		resultVideoUrl := *response.Response.ResultVideoUrl
		s.log.Info("步骤ID: %d - 腾讯混元对口型任务完成，视频URL: %s", step.ID, resultVideoUrl)

		// 将视频文件从预览URL抓取到七牛云存储
		filename := fmt.Sprintf("tencent_avatar_video_%d_%s.mp4", step.ID, jobID)
		qiniuPath := fmt.Sprintf("%s/%s", time.Now().Format("2006/01/02"), filename)
		qiniuURL, err := qiniu.FetchFile(resultVideoUrl, qiniuPath)
		if err != nil {
			s.log.Error("步骤ID: %d - 抓取视频文件到七牛云失败: %v", step.ID, err)
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, "视频转存失败")
			// 这里不直接返回错误，而是使用原始URL
			qiniuURL = resultVideoUrl
		} else {
			s.log.Info("步骤ID: %d - 视频文件已抓取到七牛云: %s", step.ID, qiniuURL)
		}

		// 更新步骤状态为完成，并将结果存入result
		resultJSON, err := json.Marshal(map[string]interface{}{
			"job_id":           jobID,
			"result_video_url": resultVideoUrl,
			"status_code":      statusCode,
			"status_msg":       getStringPtr(response.Response.StatusMsg),
		})
		if err != nil {
			s.log.Error("步骤ID: %d - 序列化结果数据失败: %v", step.ID, err)
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, "数据序列化失败")
			return
		}

		err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusDone, resultJSON)
		if err != nil {
			s.log.Error("步骤ID: %d - 更新步骤状态失败: %v", step.ID, err)
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, "状态更新失败")
			return
		}

		// 完成用户作品
		if err := s.completeTaskAndCreateUserWork(ctx, step, qiniuURL); err != nil {
			s.log.Error("步骤ID: %d - 完成用户作品失败: %v", step.ID, err)
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, "作品完成失败")
			return
		}
		if err := s.pushGetui(step, true); err != nil {
			s.log.Error("步骤ID: %d - 推送个推失败: %v", step.ID, err)
		}
		s.log.Info("步骤ID: %d - 腾讯混元对口型任务完成，result_video_url: %s, qiniu_url: %s", step.ID, resultVideoUrl, qiniuURL)

	case "FAIL":
		// 任务失败
		errorMsg := getStringPtr(response.Response.ErrorMessage)
		s.log.Error("步骤ID: %d - 腾讯混元对口型任务失败: %s", step.ID, errorMsg)
		step.ErrorMsg = fmt.Sprintf("腾讯混元对口型任务失败: %s", errorMsg)
		s.handleStepError(ctx, step, "对口型任务失败")
		return

	case "RUN":
		// 任务仍在运行中
		s.log.Info("步骤ID: %d - 腾讯混元对口型任务 %s 仍在处理中，继续等待", step.ID, jobID)
		return

	case "STOP":
		// 任务被终止
		s.log.Error("步骤ID: %d - 腾讯混元对口型任务被终止", step.ID)
		step.ErrorMsg = "腾讯混元对口型任务被终止"
		s.handleStepError(ctx, step, "任务被终止")
		return

	default:
		// 未知状态
		s.log.Error("步骤ID: %d - 腾讯混元对口型任务返回未知状态: %s", step.ID, statusCode)
		step.ErrorMsg = fmt.Sprintf("腾讯混元对口型任务返回未知状态: %s", statusCode)
		s.handleStepError(ctx, step, "任务状态异常")
		return
	}
}

// extractJobIDFromResult 从result字段中解析job_id
func (s *AvatarPictureProcessors) extractJobIDFromResult(result dto.JSONMap) (string, error) {
	if result == nil {
		return "", fmt.Errorf("result字段为空")
	}

	jobID, ok := result["job_id"].(string)
	if !ok {
		return "", fmt.Errorf("result中没有找到job_id或类型错误")
	}

	if jobID == "" {
		return "", fmt.Errorf("job_id为空")
	}

	return jobID, nil
}

// getStringPtr 安全获取字符串指针的值
func getStringPtr(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}
func (s *AvatarPictureProcessors) pushGetui(step *dto.TaskStepDTO, isSuccess bool) error {
	state := 1
	msgString := "写真制作成功"
	if !isSuccess {
		state = 2
		msgString = "写真制作失败"
	}
	payload := getui.MakePhotoPayload{
		WorkID:     step.WorkID,
		State:      state,
		PhotoCover: step.UserWork.Cover,
	}
	msg := getui.Transmission{
		Type: getui.MakePhoto,
		Msg:  msgString,
		Time: time.Now().UnixMilli(),
	}
	msg.Payload = payload
	// 发送推送
	err := s.userService.PushGetuiByUserId(step.UserWork.UserID, msg)
	if err != nil {
		return err
	}
	return nil
}
