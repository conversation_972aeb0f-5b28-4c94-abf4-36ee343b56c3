package router

import (
	"chongli/internal/app/admin/controller"
	"chongli/internal/middleware"

	"github.com/gin-gonic/gin"
)

type TemplateManageRouter struct {
	templateCtrl         *controller.TemplateManageController
	templateAIController *controller.TemplateAIController
}

// NewTemplateManageRouter 创建模板管理路由
func NewTemplateManageRouter(engine *gin.Engine, templateCtrl *controller.TemplateManageController, templateAIController *controller.TemplateAIController) *TemplateManageRouter {
	router := &TemplateManageRouter{templateCtrl: templateCtrl, templateAIController: templateAIController}

	// 模板管理相关路由（需要JWT验证）
	templateManage := engine.Group("/admin/template")
	templateManage.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		// 模板分类管理
		category := templateManage.Group("/category")
		{
			category.GET("list", router.templateCtrl.GetTemplateCategoryList)                // 分页条件查询模板分类
			category.POST("create", router.templateCtrl.CreateTemplateCategory)              // 创建模板分类
			category.PUT("update", router.templateCtrl.UpdateTemplateCategory)               // 更新模板分类
			category.DELETE("batch_delete", router.templateCtrl.BatchDeleteTemplateCategory) // 批量删除模板分类
			category.PUT("batch_update", router.templateCtrl.BatchUpdateTemplateCategory)    // 批量更新模板分类
			category.GET("main_class/list", router.templateCtrl.GetMainClassList)            // 获取主分类列表
			category.GET("all", router.templateCtrl.GetAllCategories)                        // 获取所有模板分类
		}
		// AI模板管理
		ai := templateManage.Group("template_ai")
		{
			ai.GET("list", router.templateAIController.ListByQuery)            // 根据条件查询AI模板列表
			ai.GET("detail/:id", router.templateAIController.GetByID)          // 根据ID获取AI模板
			ai.POST("create", router.templateAIController.Create)              // 创建AI模板
			ai.POST("update", router.templateAIController.Update)              // 更新AI模板
			ai.DELETE("delete/:id", router.templateAIController.Delete)        // 删除AI模板
			ai.POST("update_script", router.templateAIController.UpdateScript) // 更新AI模板脚本
			ai.GET("all", router.templateAIController.GetAllTemplates)         // 获取所有AI模板
			ai.PUT("batch_update", router.templateAIController.BatchUpdate)    // 批量更新AI模板
		}
	}

	return router
}
