package controller

import (
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AddGoodsVipRequest struct {
	GoodsID        string  `json:"goods_id" form:"goods_id" binding:"required"`
	Title          string  `json:"title" form:"title" binding:"required"`
	Price          float64 `json:"price" form:"price" binding:"required"`
	VipType        int     `json:"vip_type" form:"vip_type" binding:"required"`
	Sort           int     `json:"sort" form:"sort" binding:"required"`
	Version        string  `json:"version" form:"version" binding:"required"`
	Channel        string  `json:"channel" form:"channel" binding:"required"`
	IsChannel      int     `json:"is_channel" form:"is_channel" `
	BindKey        string  `json:"bind_key" form:"bind_key"`
	BindValue      string  `json:"bind_value" form:"bind_value"`
	MonthlyDiamond int     `json:"monthly_diamond" form:"monthly_diamond" binding:"required,gt=0"`
	IsDelete       int8    `json:"is_delete" form:"is_delete" default:"1" binding:"oneof=-1 1"`
	Page           int     `json:"page" form:"page"`
	Size           int     `json:"size" form:"size"`
}

type EditGoodsVipRequest struct {
	ID int `json:"id" form:"id" binding:"required"`
	AddGoodsVipRequest
}

type GoodsVipListRequest struct {
	ID      int64  `json:"id" form:"id"`
	GoodsID string `json:"goods_id" form:"goods_id"`
	Title   string `json:"title" form:"title"`
	Version string `json:"version" form:"version"`
	Channel string `json:"channel" form:"channel"`
	Page    int    `json:"page" form:"page" binding:"numeric,required,gt=0"`
	Size    int    `json:"size" form:"size" binding:"numeric,required,gt=0"`
}

type GoodsVipIDRequest struct {
	ID int64 `json:"id" form:"id" binding:"numeric,required,gt=0"`
}

type GoodsVipResponse struct {
	ID             int64   `json:"id"`
	GoodsID        string  `json:"goods_id"`
	Title          string  `json:"title"`
	Price          float64 `json:"price"`
	VipType        int     `json:"vip_type"`
	VipTypeName    string  `json:"vip_type_name"`
	Sort           int     `json:"sort"`
	Version        string  `json:"version"`
	Channel        string  `json:"channel"`
	CreatedAt      string  `json:"createdAt"`
	UpdatedAt      string  `json:"updatedAt"`
	IsChannel      int     `json:"is_channel"`
	BindKey        string  `json:"bind_key"`
	BindValue      string  `json:"bind_value"`
	MonthlyDiamond int64   `json:"monthly_diamond"`
	IsDelete       int8    `json:"is_delete"`
	IsDisplay      int8    `json:"is_display" default:"1"`
}

type GoodsBaseController struct {
	goodsRepo repo.GoodsVipRepo
}

func NewGoodsBaseController(goodsRepo repo.GoodsVipRepo) *GoodsBaseController {
	return &GoodsBaseController{goodsRepo: goodsRepo}
}

func (a *GoodsBaseController) GetByGoodsId(c *gin.Context) {
	goodsId := c.Param("id")
	if goodsId == "" {
		response.Response(c, nil, nil, errpkg.NewMiddleError("goods_id不能为空"), response.WithSLSLog)
		return
	}

	id, err := strconv.ParseInt(goodsId, 10, 64)
	if err != nil {
		response.Response(c, nil, nil, errpkg.NewMiddleError("goods_id格式错误: "+err.Error()), response.WithSLSLog)
		return
	}

	data, err := a.goodsRepo.GetGoods(&dto.VipGoodsInfoDto{Id: int(id)})
	if err != nil {
		response.Response(c, nil, nil, errpkg.NewHighError(err.Error()), response.WithSLSLog)
		return
	}

	response.Response(c, nil, data, nil, response.WithSLSLog)
}

func (a *GoodsBaseController) Add(c *gin.Context) {
	var req AddGoodsVipRequest
	if errBind := c.ShouldBind(&req); errBind != nil {
		response.Response(c, req, nil, errpkg.NewMiddleError("参数绑定失败: "+errBind.Error()), response.WithSLSLog)
		return
	}

	goodsCreateDto := &dto.VipGoodsCreateDto{
		GoodsId:        req.GoodsID,
		Title:          req.Title,
		Price:          req.Price,
		VipType:        req.VipType,
		Version:        req.Version,
		Channel:        req.Channel,
		Sort:           req.Sort,
		MonthlyDiamond: req.MonthlyDiamond,
		IsDelete:       model.StatusFlag(req.IsDelete),
	}
	result, err := a.goodsRepo.CreateGoods(goodsCreateDto)
	if err != nil {
		response.Response(c, req, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}
	response.Response(c, req, result, nil, response.WithSLSLog)
}

func (a *GoodsBaseController) Edit(c *gin.Context) {
	var req EditGoodsVipRequest
	if errBind := c.ShouldBind(&req); errBind != nil {
		response.Response(c, req, nil, errpkg.NewMiddleError("参数绑定失败: "+errBind.Error()), response.WithSLSLog)
		return
	}

	// 使用Update方法更新商品
	condition := map[string]any{"id": req.ID}
	updateData := map[string]any{
		"goods_id":        req.GoodsID,
		"title":           req.Title,
		"price":           req.Price,
		"sort":            req.Sort,
		"vip_type":        req.VipType,
		"version":         req.Version,
		"version_int":     utils.VersionToVersionInt(req.Version),
		"channel":         req.Channel,
		"monthly_diamond": req.MonthlyDiamond,
		"is_delete":       model.StatusFlag(req.IsDelete),
	}
	err := a.goodsRepo.Update(condition, updateData)
	if err != nil {
		response.Response(c, req, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}
	response.Response(c, req, nil, nil, response.WithSLSLog)
}

func (a *GoodsBaseController) Delete(c *gin.Context) {
	var req dto.VipGoodsInfoDto
	if errBind := c.ShouldBindQuery(&req); errBind != nil {
		response.Response(c, req, nil, errpkg.NewMiddleError("参数绑定失败: "+errBind.Error()), response.WithSLSLog)
		return
	}
	if req.Id == 0 {
		response.Response(c, req, nil, errpkg.NewMiddleError("id不能为空"), response.WithSLSLog)
		return
	}

	err := a.goodsRepo.Delete(&req)
	if err != nil {
		response.Response(c, req, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}
	response.Response(c, req, nil, nil, response.WithSLSLog)
}

func (a *GoodsBaseController) List(c *gin.Context) {
	var req dto.VipGoodsInfoDto
	if errBind := c.ShouldBind(&req); errBind != nil {
		response.Response(c, req, nil, errpkg.NewMiddleError("参数绑定失败: "+errBind.Error()), response.WithSLSLog)
		return
	}

	data, err := a.goodsRepo.GetGoodsList(&req)
	if err != nil {
		response.Response(c, req, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}
	count, err := a.goodsRepo.Count(&req)
	if err != nil {
		response.Response(c, req, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}
	response.Response(c, req, map[string]any{"list": data, "count": count}, nil, response.WithSLSLog)
}

func (a *GoodsBaseController) Select(c *gin.Context) {
	// 获取所有商品（不分页）
	req := &dto.VipGoodsInfoDto{}
	list, err := a.goodsRepo.GetGoodsList(req)
	if err != nil {
		response.Response(c, req, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}
	response.Response(c, req, list, nil, response.WithSLSLog)
}

func (a *GoodsBaseController) AddChannel(ctx *gin.Context) {
	var req struct {
		GoodsId   int   `json:"goods_id" form:"goods_id"`
		ChannelId []int `json:"channel_id" form:"channel_id"`
	}
	if err := ctx.ShouldBind(&req); err != nil {
		response.Response(ctx, req, nil, errpkg.NewMiddleError("参数绑定失败: "+err.Error()), response.WithSLSLog)
		return
	}

	// 逐个创建渠道绑定
	var results []*dto.GoodsVipChannelDto
	for _, channelID := range req.ChannelId {
		channelDto := &dto.GoodsVipChannelCreateDto{
			GoodsID:   req.GoodsId,
			ChannelID: channelID,
		}
		result, err := a.goodsRepo.CreateChannel(channelDto)
		if err != nil {
			response.Response(ctx, req, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
			return
		}
		results = append(results, result)
	}
	response.Response(ctx, req, results, nil, response.WithSLSLog)
}

func (a *GoodsBaseController) GetChannel(ctx *gin.Context) {
	var req struct {
		GoodsId int `json:"goods_id" form:"goods_id"`
	}
	if err := ctx.ShouldBind(&req); err != nil {
		response.Response(ctx, req, nil, errpkg.NewMiddleError("参数绑定失败: "+err.Error()), response.WithSLSLog)
		return
	}
	// 查询商品的渠道绑定
	channelReq := dto.GoodsVipChannelReq{
		GoodsID: int64(req.GoodsId),
	}
	data, err := a.goodsRepo.ListChannelGoods(channelReq)
	if err != nil {
		response.Response(ctx, req, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}
	response.Response(ctx, req, data, nil, response.WithSLSLog)
}
